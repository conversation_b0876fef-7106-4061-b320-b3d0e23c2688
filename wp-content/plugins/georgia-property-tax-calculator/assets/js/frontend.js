/**
 * Frontend JavaScript for Georgia Property Tax Calculator
 */

(function($) {
    'use strict';
    
    $(document).ready(function() {
        initializeCalculator();
    });
    
    // Debounce utility
    function debounce(func, wait) {
        var timeout;
        return function() {
            var context = this, args = arguments;
            clearTimeout(timeout);
            timeout = setTimeout(function() {
                func.apply(context, args);
            }, wait);
        };
    }
    
    function initializeCalculator() {
        // Property type button toggle
        $('.gptc-button-option').on('click', function() {
            $('.gptc-button-option').removeClass('gptc-button-active');
            $(this).addClass('gptc-button-active');
        });
        // Form validation with debounce on submit
        var debouncedSubmit = debounce(function() {
            if (validateForm()) {
                submitForm();
            }
        }, 500);
        $('#gptc-calculator-form').off('submit').on('submit', function(e) {
            e.preventDefault();
            debouncedSubmit();
        });
        // Real-time validation
        $('.gptc-input[required]').on('blur', function() {
            validateField($(this));
        });
        // Format market value input
        $('#gptc_market_value').on('input', function() {
            formatNumberInput($(this));
        });
        // Auto-suggest for address (if Google Places API is available)
        if (typeof google !== 'undefined' && google.maps && google.maps.places) {
            initializeAddressAutocomplete();
        }
    }
    
    function validateForm() {
        var isValid = true;
        var $form = $('#gptc-calculator-form');
        
        // Clear previous errors
        $('.gptc-field-error').remove();
        $('.gptc-input').removeClass('gptc-input-error');
        
        // Validate required fields
        $form.find('.gptc-input[required]').each(function() {
            if (!validateField($(this))) {
                isValid = false;
            }
        });
        
        // Validate email format
        var $email = $('#gptc_email');
        if ($email.val() && !isValidEmail($email.val())) {
            showFieldError($email, 'Please enter a valid email address.');
            isValid = false;
        }
        
        // Validate market value if provided
        var $marketValue = $('#gptc_market_value');
        if ($marketValue.val() && (isNaN($marketValue.val()) || parseFloat($marketValue.val()) < 0)) {
            showFieldError($marketValue, 'Please enter a valid property value.');
            isValid = false;
        }
        
        return isValid;
    }
    
    function validateField($field) {
        var value = $field.val().trim();
        var isValid = true;
        
        if ($field.prop('required') && !value) {
            showFieldError($field, 'This field is required.');
            isValid = false;
        } else {
            clearFieldError($field);
        }
        
        return isValid;
    }
    
    function showFieldError($field, message) {
        clearFieldError($field);
        $field.addClass('gptc-input-error');
        $field.after('<div class="gptc-field-error">' + message + '</div>');
    }
    
    function clearFieldError($field) {
        $field.removeClass('gptc-input-error');
        $field.siblings('.gptc-field-error').remove();
    }
    
    function isValidEmail(email) {
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    function formatNumberInput($input) {
        var value = $input.val().replace(/[^\d]/g, '');
        if (value) {
            $input.val(parseInt(value, 10));
        }
    }
    
    function submitForm() {
        var $form = $('#gptc-calculator-form');
        var $submitBtn = $form.find('.gptc-submit-btn');
        var $btnText = $submitBtn.find('.gptc-btn-text');
        var $btnLoading = $submitBtn.find('.gptc-btn-loading');
        var $result = $('#gptc-result');
        var $error = $('#gptc-error');
        
        // Reset previous results
        $result.hide();
        $error.hide();
        
        // Show loading state
        $btnText.hide();
        $btnLoading.show();
        $submitBtn.prop('disabled', true);
        
        // Collect form data
        var formData = {
            action: 'gptc_calculate_tax',
            nonce: $form.find('[name="gptc_nonce"]').val(),
            name: $('#gptc_name').val(),
            email: $('#gptc_email').val(),
            address: $('#gptc_address').val(),
            market_value: $('#gptc_market_value').val(),
            property_type: $('input[name="property_type"]:checked').val(),
            state: $('#gptc_state').val()
        };
        
        // Submit via AJAX
        $.post(gptc_ajax.ajax_url, formData)
            .done(function(response) {
                if (response.success) {
                    $result.html(response.data.html).show();
                    
                    // Scroll to result with smooth animation
                    $('html, body').animate({
                        scrollTop: $result.offset().top - 50
                    }, 500);
                    
                    // Track conversion (if analytics is available)
                    trackConversion(response.data);
                    
                    // After AJAX success, update savings preview
                    if (response.success && response.data && response.data.potential_savings) {
                        var savings = response.data.potential_savings;
                        var formatted = '$' + Number(savings).toLocaleString() + '/year!';
                        $('#gptc-savings-amount').text(formatted);
                        $('#gptc-savings-preview').fadeIn(200);
                    } else {
                        $('#gptc-savings-preview').hide();
                        $('#gptc-savings-amount').text('');
                    }
                    
                } else {
                    var errorMessage = response.data || gptc_ajax.error_text;
                    $error.html('<p><strong>Error:</strong> ' + errorMessage + '</p>').show();
                }
            })
            .fail(function(xhr, status, error) {
                console.error('GPTC Ajax Error:', error);
                $error.html('<p><strong>Error:</strong> ' + gptc_ajax.error_text + '</p>').show();
            })
            .always(function() {
                // Reset button state
                $btnText.show();
                $btnLoading.hide();
                $submitBtn.prop('disabled', false);
            });
    }
    
    function initializeAddressAutocomplete() {
        var $addressInput = $('#gptc_address');
        
        if ($addressInput.length) {
            var autocomplete = new google.maps.places.Autocomplete($addressInput[0], {
                types: ['address'],
                componentRestrictions: { country: 'us' }
            });
            
            autocomplete.addListener('place_changed', function() {
                var place = autocomplete.getPlace();
                
                if (place.geometry) {
                    // Store coordinates for more accurate API calls
                    $addressInput.data('lat', place.geometry.location.lat());
                    $addressInput.data('lng', place.geometry.location.lng());
                }
            });
        }
    }
    
    function trackConversion(data) {
        // Google Analytics tracking
        if (typeof gtag !== 'undefined') {
            gtag('event', 'conversion', {
                'event_category': 'Property Tax Calculator',
                'event_label': data.county + ' County',
                'value': data.potential_savings
            });
        }
        
        // Facebook Pixel tracking
        if (typeof fbq !== 'undefined') {
            fbq('track', 'Lead', {
                content_name: 'Property Tax Calculator',
                content_category: 'Tax Services',
                value: data.potential_savings,
                currency: 'USD'
            });
        }
        
        // Custom tracking hook for other analytics
        $(document).trigger('gptc_conversion', [data]);
    }
    
    // Utility functions
    function formatCurrency(amount) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount);
    }
    
    function formatNumber(number) {
        return new Intl.NumberFormat('en-US').format(number);
    }
    
    // Add CSS for validation errors
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .gptc-input-error {
                border-color: #e74c3c !important;
                box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1) !important;
            }
            .gptc-field-error {
                color: #e74c3c;
                font-size: 12px;
                margin-top: 5px;
                display: block;
            }
        `)
        .appendTo('head');
    
})(jQuery);
