/**
 * Frontend styles for Georgia Property Tax Calculator
 * Matches the provided design with blue and yellow color scheme
 */

/* Main container */
.gptc-form-container {
    max-width: 480px;
    margin: 40px auto;
    padding: 0;
    background: transparent;
    box-shadow: none;
}

/* Form header */
.gptc-form-header {
    background: #1E3A8A;
    border-radius: 18px 18px 0 0;
    padding: 28px 32px 18px 32px;
    text-align: left;
}

.gptc-form-title {
    color: #fff !important;
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    letter-spacing: 0.01em;
}

/* Form wrapper */
.gptc-form-wrapper {
    background: #fff;
    border-radius: 0 0 18px 18px;
    box-shadow: 0 4px 24px rgba(30,58,138,0.08);
    padding: 32px 32px 24px 32px;
    display: block;
}

/* Form content */
.gptc-form-content {
    padding: 0;
}

/* Form elements */
.gptc-form {
    max-width: 100%;
    width: 100%;
}

.gptc-form-row {
    display: flex;
    gap: 18px;
    margin-bottom: 18px;
}

.gptc-form-group {
    margin-bottom: 22px;
}

.gptc-form-group.gptc-half {
    flex: 1;
}

.gptc-form-group label {
    display: block;
    margin-bottom: 7px;
    font-weight: 600;
    color: #1E3A8A;
    font-size: 15px;
}

.required {
    color: #e74c3c;
}

/* Input styles */
.gptc-input,
.gptc-select {
    width: 100%;
    padding: 13px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s;
    box-sizing: border-box;
    background: #f8fafc;
}

.gptc-input:focus,
.gptc-select:focus {
    outline: none;
    border-color: #1E3A8A;
    background: #fff;
    box-shadow: 0 0 0 2px #1E3A8A22;
}

.gptc-input-wrapper {
    position: relative;
}

.gptc-input-prefix {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    font-weight: 600;
    z-index: 2;
}

.gptc-input-with-prefix {
    padding-left: 35px;
}

.gptc-help-text {
    color: #888;
    font-size: 13px;
    margin-top: 3px;
}

/* Button group for property type */
.gptc-button-group {
    display: flex;
    gap: 10px;
}

.gptc-button-group input[type="radio"] {
    display: none;
}

.gptc-button-option {
    display: inline-block;
    padding: 10px 22px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    background: #fff;
    color: #1E3A8A;
    font-weight: 600;
    font-size: 15px;
    cursor: pointer;
    transition: all 0.2s;
    margin-right: 0;
}

.gptc-button-option:hover {
    background: #f8f9fa;
}

.gptc-button-option.gptc-button-active,
.gptc-button-option:active,
.gptc-button-option:focus {
    background: #1E3A8A;
    color: #fff;
    border-color: #1E3A8A;
}

/* Savings preview */
.gptc-savings-preview {
    text-align: center;
    margin: 30px 0;
    padding: 20px;
    background: linear-gradient(135deg, #10B981, #059669);
    border-radius: 10px;
    color: white;
}

.gptc-savings-text {
    font-size: 18px;
    font-weight: 500;
}

.gptc-savings-amount {
    display: block;
    font-size: 28px;
    font-weight: 700;
    margin-top: 5px;
}

/* Submit button */
.gptc-form-submit {
    text-align: center;
    margin-top: 30px;
}

.gptc-form .gptc-submit-btn,
.gptc-form button[type="submit"],
.gptc-form input[type="submit"] {
    background: #FFC107;
    color: #1E3A8A;
    font-weight: 700;
    font-size: 1.1em;
    border: none;
    border-radius: 8px;
    padding: 13px 0;
    width: 100%;
    margin-top: 10px;
    box-shadow: 0 2px 8px #1e3a8a11;
    cursor: pointer;
    transition: background 0.2s, color 0.2s;
}

.gptc-submit-btn:hover {
    background: #e6940a;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 165, 0, 0.3);
}

.gptc-submit-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.gptc-btn-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.gptc-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: gptc-spin 1s linear infinite;
}

@keyframes gptc-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Illustration section */
.gptc-form-illustration {
    flex: 0 0 400px;
    background: linear-gradient(135deg, #1E3A8A, #3B82F6);
    padding: 40px 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.gptc-illustration-content {
    position: relative;
    width: 100%;
    height: 300px;
}

.gptc-house-icon {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 3;
}

.gptc-calculator-icon {
    position: absolute;
    bottom: 80px;
    left: 20px;
    z-index: 2;
}

.gptc-dollar-icon {
    position: absolute;
    top: 60px;
    right: 30px;
    z-index: 2;
}

.gptc-person-icon {
    position: absolute;
    bottom: 20px;
    right: 40px;
    z-index: 1;
}

/* Result styles */
.gptc-result,
.gptc-error {
    margin-top: 30px;
    padding: 20px;
    border-radius: 8px;
}

.gptc-error {
    background: #fee;
    border: 1px solid #fcc;
    color: #c33;
}

/* Results Card */
.gptc-result-container {
    background: #fff;
    border-radius: 18px;
    box-shadow: 0 4px 24px rgba(30,58,138,0.10);
    padding: 32px 32px 24px 32px;
    margin: 32px auto 0 auto;
    max-width: 480px;
    width: 100%;
    transition: box-shadow 0.2s;
}
.gptc-result-header {
    background: #1E3A8A;
    color: #fff;
    border-radius: 14px 14px 0 0;
    padding: 22px 28px 12px 28px;
    margin: -32px -32px 24px -32px;
    text-align: left;
}
.gptc-result-header h4 {
    margin: 0 0 6px 0;
    font-size: 1.3em;
    font-weight: 700;
    color: #fff;
}
.gptc-result-address {
    font-size: 1.1em;
    font-weight: 600;
    margin-bottom: 2px;
}
.gptc-result-county {
    font-size: 1em;
    color: #c7d2fe;
    margin-bottom: 0;
}
.gptc-result-grid {
    display: flex;
    flex-direction: column;
    gap: 18px;
}
.gptc-result-item {
    background: #f8fafc;
    border-radius: 10px;
    padding: 18px 20px;
    box-shadow: 0 1px 4px #1e3a8a08;
    font-size: 1.1em;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}
.gptc-result-label {
    color: #1E3A8A;
    font-weight: 600;
    font-size: 1em;
    margin-bottom: 4px;
}
.gptc-result-value {
    font-size: 1.3em;
    font-weight: 700;
    color: #222;
}
.gptc-result-item.gptc-highlight {
    background: #FFC107;
    color: #1E3A8A;
    font-weight: 700;
    box-shadow: 0 2px 8px #ffc10733;
}
.gptc-result-item.gptc-highlight .gptc-result-label {
    color: #1E3A8A;
}
.gptc-result-item.gptc-highlight .gptc-result-value {
    color: #1E3A8A;
}

/* Error Message UX */
.gptc-error-message {
    background: #fee2e2;
    color: #b91c1c;
    border: 1.5px solid #fca5a5;
    border-radius: 10px;
    padding: 18px 22px;
    margin: 28px auto 0 auto;
    max-width: 480px;
    font-size: 1.08em;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 2px 8px #b91c1c11;
    transition: opacity 0.2s;
}
.gptc-error-message:before {
    content: "\26A0";
    font-size: 1.5em;
    color: #b91c1c;
    margin-right: 6px;
}

.gptc-error-message.hide,
.gptc-result-container.hide {
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s;
}

.gptc-savings-section {
    background: #f8f9fa;
    padding: 20px;
}

.gptc-savings-header h5 {
    margin: 0 0 15px 0;
    color: #1E3A8A;
    font-size: 18px;
}

.gptc-savings-amount {
    text-align: center;
    margin-bottom: 20px;
}

.gptc-savings-annual {
    margin-bottom: 10px;
}

.gptc-savings-label {
    display: block;
    font-size: 16px;
    color: #666;
}

.gptc-savings-value {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: #10B981;
}

.gptc-cta-section ul {
    margin: 15px 0;
    padding-left: 20px;
}

.gptc-cta-section li {
    margin-bottom: 8px;
}

.gptc-contact-info {
    display: flex;
    gap: 30px;
    justify-content: center;
    margin-top: 20px;
    flex-wrap: wrap;
}

.gptc-contact-item {
    text-align: center;
}

.gptc-contact-item a {
    color: #1E3A8A;
    text-decoration: none;
    font-weight: 600;
}

.gptc-disclaimer {
    padding: 15px 20px;
    background: #f1f5f9;
    border-top: 1px solid #e1e5e9;
}

/* Responsive design */
@media (max-width: 600px) {
    .gptc-form-container {
        max-width: 100%;
        margin: 0;
        padding: 0;
    }
    .gptc-form-header, .gptc-form-wrapper {
        padding: 18px 8px 12px 8px;
        border-radius: 0;
    }
    .gptc-form-wrapper {
        box-shadow: none;
    }
    .gptc-result-container {
        max-width: 100%;
        padding: 18px 8px 12px 8px;
    }
    .gptc-result-header {
        padding: 14px 8px 8px 8px;
        margin: -18px -8px 18px -8px;
    }
    .gptc-error-message {
        max-width: 100%;
        padding: 12px 8px;
    }
}
