<?php
/**
 * Plugin Name: Georgia Property Tax Calculator
 * Plugin URI: https://propertytaxreportsusa.com
 * Description: A comprehensive property tax calculator for Georgia with Google Maps API integration, RentCast API for property values, and email notifications.
 * Version: 1.0.0
 * Author: Property Tax Reports USA
 * Author URI: https://propertytaxreportsusa.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: georgia-property-tax-calculator
 * Domain Path: /languages
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('GPTC_VERSION', '1.0.0');
define('GPTC_PLUGIN_URL', plugin_dir_url(__FILE__));
define('GPTC_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('GPTC_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main Georgia Property Tax Calculator Class
 */
class GeorgiaPropertyTaxCalculator {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('init', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    /**
     * Initialize the plugin
     */
    public function init() {
        // Load text domain for translations
        load_plugin_textdomain('georgia-property-tax-calculator', false, dirname(plugin_basename(__FILE__)) . '/languages');
        
        // Initialize components
        $this->includes();
        $this->init_hooks();

        // Schedule email notifications hook
        add_action('gptc_send_notifications', array($this, 'send_email_notifications'), 10, 2);
    }
    
    /**
     * Include required files
     */
    private function includes() {
        require_once GPTC_PLUGIN_PATH . 'includes/class-gptc-admin.php';
        require_once GPTC_PLUGIN_PATH . 'includes/class-gptc-frontend.php';
        require_once GPTC_PLUGIN_PATH . 'includes/class-gptc-database.php';
        require_once GPTC_PLUGIN_PATH . 'includes/class-gptc-api-handler.php';
        require_once GPTC_PLUGIN_PATH . 'includes/class-gptc-email.php';
        require_once GPTC_PLUGIN_PATH . 'includes/class-gptc-calculator.php';
        require_once GPTC_PLUGIN_PATH . 'includes/class-gptc-shortcode.php';
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Initialize admin functionality
        if (is_admin()) {
            new GPTC_Admin();
        }
        
        // Initialize frontend functionality
        new GPTC_Frontend();
        new GPTC_Shortcode();
        
        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_assets'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
        
        // AJAX handlers
        add_action('wp_ajax_gptc_calculate_tax', array($this, 'ajax_calculate_tax'));
        add_action('wp_ajax_nopriv_gptc_calculate_tax', array($this, 'ajax_calculate_tax'));
    }
    
    /**
     * Enqueue frontend assets
     */
    public function enqueue_frontend_assets() {
        wp_enqueue_style(
            'gptc-frontend-style',
            GPTC_PLUGIN_URL . 'assets/css/frontend.css',
            array(),
            GPTC_VERSION
        );
        
        wp_enqueue_script(
            'gptc-frontend-script',
            GPTC_PLUGIN_URL . 'assets/js/frontend.js',
            array('jquery'),
            GPTC_VERSION,
            true
        );
        
        // Localize script for AJAX
        wp_localize_script('gptc-frontend-script', 'gptc_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('gptc_nonce'),
            'loading_text' => __('Calculating...', 'georgia-property-tax-calculator'),
            'error_text' => __('An error occurred. Please try again.', 'georgia-property-tax-calculator')
        ));
    }
    
    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets($hook) {
        // Only load on our admin pages
        if (strpos($hook, 'gptc') === false) {
            return;
        }
        
        wp_enqueue_style(
            'gptc-admin-style',
            GPTC_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            GPTC_VERSION
        );
        
        wp_enqueue_script(
            'gptc-admin-script',
            GPTC_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery'),
            GPTC_VERSION,
            true
        );
    }
    
    /**
     * AJAX handler for tax calculation
     */
    public function ajax_calculate_tax() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'gptc_nonce')) {
            wp_die(__('Security check failed', 'georgia-property-tax-calculator'));
        }

        // Rate limiting check
        if (!$this->check_rate_limit()) {
            wp_send_json_error(__('Too many requests. Please wait before submitting again.', 'georgia-property-tax-calculator'));
        }

        // Sanitize and validate input data
        $data = $this->sanitize_form_data($_POST);

        if (!$data) {
            wp_send_json_error(__('Invalid form data provided.', 'georgia-property-tax-calculator'));
        }

        // Validate required fields
        $validation_result = $this->validate_form_data($data);
        if (!$validation_result['valid']) {
            wp_send_json_error($validation_result['message']);
        }

        // Initialize calculator
        $calculator = new GPTC_Calculator();
        $result = $calculator->calculate_tax($data);

        if ($result['success']) {
            // Store submission in database
            $database = new GPTC_Database();
            $submission_id = $database->store_submission($data, $result['data']);

            // Send email notifications (in background if possible)
            wp_schedule_single_event(time(), 'gptc_send_notifications', array($data, $result['data']));

            wp_send_json_success($result['data']);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    /**
     * Sanitize form data
     */
    private function sanitize_form_data($post_data) {
        if (!is_array($post_data)) {
            return false;
        }

        $data = array(
            'name' => isset($post_data['name']) ? sanitize_text_field($post_data['name']) : '',
            'email' => isset($post_data['email']) ? sanitize_email($post_data['email']) : '',
            'address' => isset($post_data['address']) ? sanitize_textarea_field($post_data['address']) : '',
            'market_value' => isset($post_data['market_value']) ? floatval($post_data['market_value']) : 0,
            'property_type' => isset($post_data['property_type']) ? sanitize_text_field($post_data['property_type']) : 'home',
            'state' => isset($post_data['state']) ? sanitize_text_field($post_data['state']) : 'GA'
        );

        // Additional sanitization
        $data['property_type'] = in_array($data['property_type'], array('home', 'commercial')) ? $data['property_type'] : 'home';
        $data['state'] = strtoupper($data['state']);
        $data['market_value'] = max(0, $data['market_value']);

        return $data;
    }

    /**
     * Validate form data
     */
    private function validate_form_data($data) {
        $errors = array();

        // Required field validation
        if (empty($data['name'])) {
            $errors[] = __('Name is required.', 'georgia-property-tax-calculator');
        }

        if (empty($data['email'])) {
            $errors[] = __('Email is required.', 'georgia-property-tax-calculator');
        } elseif (!is_email($data['email'])) {
            $errors[] = __('Please enter a valid email address.', 'georgia-property-tax-calculator');
        }

        if (empty($data['address'])) {
            $errors[] = __('Property address is required.', 'georgia-property-tax-calculator');
        }

        // Length validation
        if (strlen($data['name']) > 100) {
            $errors[] = __('Name is too long.', 'georgia-property-tax-calculator');
        }

        if (strlen($data['address']) > 500) {
            $errors[] = __('Address is too long.', 'georgia-property-tax-calculator');
        }

        // Market value validation
        if ($data['market_value'] > 0 && $data['market_value'] < 1000) {
            $errors[] = __('Market value seems too low. Please check your input.', 'georgia-property-tax-calculator');
        }

        if ($data['market_value'] > *********) {
            $errors[] = __('Market value seems too high. Please check your input.', 'georgia-property-tax-calculator');
        }

        return array(
            'valid' => empty($errors),
            'message' => empty($errors) ? '' : implode(' ', $errors)
        );
    }

    /**
     * Check rate limiting
     */
    private function check_rate_limit() {
        $ip = $this->get_client_ip();
        $transient_key = 'gptc_rate_limit_' . md5($ip);
        $requests = get_transient($transient_key);

        if ($requests === false) {
            $requests = 1;
            set_transient($transient_key, $requests, 300); // 5 minutes
            return true;
        }

        if ($requests >= 5) { // Max 5 requests per 5 minutes
            return false;
        }

        set_transient($transient_key, $requests + 1, 300);
        return true;
    }

    /**
     * Get client IP address
     */
    private function get_client_ip() {
        $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');

        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }

        return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '0.0.0.0';
    }

    /**
     * Send email notifications (scheduled action)
     */
    public function send_email_notifications($data, $result_data) {
        $email = new GPTC_Email();
        $email->send_notifications($data, $result_data);
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Ensure required classes are loaded
        $this->includes();
        // Create database tables
        $database = new GPTC_Database();
        $database->create_tables();
        // Set default options
        $this->set_default_options();
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Set default plugin options
     */
    private function set_default_options() {
        $default_options = array(
            'google_maps_api_key' => '',
            'rentcast_api_key' => '',
            'notification_email' => '<EMAIL>',
            'company_name' => 'Property Tax Reports USA',
            'company_phone' => '************',
            'company_logo_url' => '',
            'email_from_name' => 'Property Tax Reports USA',
            'email_from_email' => '<EMAIL>'
        );
        
        foreach ($default_options as $key => $value) {
            if (get_option('gptc_' . $key) === false) {
                add_option('gptc_' . $key, $value);
            }
        }
    }
}

// Initialize the plugin
new GeorgiaPropertyTaxCalculator();
