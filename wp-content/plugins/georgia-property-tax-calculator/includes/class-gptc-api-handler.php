<?php
/**
 * API handler for Georgia Property Tax Calculator
 * Handles Google Maps and RentCast API integrations
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class GPTC_API_Handler {
    
    private $google_maps_api_key;
    private $rentcast_api_key;
    
    public function __construct() {
        $this->google_maps_api_key = get_option('gptc_google_maps_api_key', '');
        $this->rentcast_api_key = get_option('gptc_rentcast_api_key', '');
    }
    
    /**
     * Geocode address using Google Maps API
     */
    public function geocode_address($address) {
        if (!get_option('gptc_enable_google_maps_api', '1')) {
            return array(
                'success' => false,
                'error' => __('Google Maps API is disabled in settings.', 'georgia-property-tax-calculator')
            );
        }
        if (empty($this->google_maps_api_key)) {
            return array(
                'success' => false,
                'error' => __('Google Maps API key not configured', 'georgia-property-tax-calculator')
            );
        }
        
        $address = urlencode($address);
        $url = "https://maps.googleapis.com/maps/api/geocode/json?address={$address}&key={$this->google_maps_api_key}";
        
        $response = wp_remote_get($url, array(
            'timeout' => 30,
            'headers' => array(
                'User-Agent' => 'Georgia Property Tax Calculator/1.0'
            )
        ));
        
        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'error' => $response->get_error_message()
            );
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if (!$data || $data['status'] !== 'OK' || empty($data['results'])) {
            return array(
                'success' => false,
                'error' => __('Address not found or invalid', 'georgia-property-tax-calculator')
            );
        }
        
        $result = $data['results'][0];
        $county = $this->extract_county_from_geocode($result);
        
        return array(
            'success' => true,
            'data' => array(
                'formatted_address' => $result['formatted_address'],
                'latitude' => $result['geometry']['location']['lat'],
                'longitude' => $result['geometry']['location']['lng'],
                'county' => $county,
                'components' => $result['address_components']
            )
        );
    }
    
    /**
     * Extract county from geocoding result
     */
    private function extract_county_from_geocode($result) {
        foreach ($result['address_components'] as $component) {
            if (in_array('administrative_area_level_2', $component['types'])) {
                // Remove " County" suffix if present
                $county = str_replace(' County', '', $component['long_name']);
                return $county;
            }
        }
        
        return null;
    }
    
    /**
     * Get property value from RentCast API
     */
    public function get_property_value($address, $latitude = null, $longitude = null, $details = array()) {
        if (!get_option('gptc_enable_rentcast_api', '1')) {
            return array(
                'success' => false,
                'error' => __('RentCast API is disabled in settings.', 'georgia-property-tax-calculator')
            );
        }
        if (empty($this->rentcast_api_key)) {
            return array(
                'success' => false,
                'error' => __('RentCast API key not configured', 'georgia-property-tax-calculator')
            );
        }
        // Build query for /v1/avm/value endpoint
        $query = array('address' => $address);
        // Optionally add property details for better accuracy
        if (!empty($details['propertyType'])) $query['propertyType'] = $details['propertyType'];
        if (!empty($details['bedrooms'])) $query['bedrooms'] = $details['bedrooms'];
        if (!empty($details['bathrooms'])) $query['bathrooms'] = $details['bathrooms'];
        if (!empty($details['squareFootage'])) $query['squareFootage'] = $details['squareFootage'];
        $url = 'https://api.rentcast.io/v1/avm/value?' . http_build_query($query);
        $response = wp_remote_get($url, array(
            'timeout' => 30,
            'headers' => array(
                'X-Api-Key' => $this->rentcast_api_key,
                'User-Agent' => 'Georgia Property Tax Calculator/1.0',
                'Accept' => 'application/json'
            )
        ));
        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'error' => $response->get_error_message()
            );
        }
        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        if ($status_code === 200 && isset($data['valueEstimate'])) {
            return array(
                'success' => true,
                'data' => array(
                    'value' => $data['valueEstimate'],
                    'source' => 'rentcast',
                    'comps' => isset($data['comparables']) ? $data['comparables'] : array()
                )
            );
        } elseif ($status_code === 200 && (isset($data['price']) || (isset($data['priceRangeLow']) && isset($data['priceRangeHigh'])))) {
            // Handle cases where the API returns 'price' and/or price range instead of 'valueEstimate'
            $price = isset($data['price']) ? $data['price'] : null;
            $rangeLow = isset($data['priceRangeLow']) ? $data['priceRangeLow'] : null;
            $rangeHigh = isset($data['priceRangeHigh']) ? $data['priceRangeHigh'] : null;
            $msg = __('RentCast API key is valid. ', 'georgia-property-tax-calculator');
            if ($price) {
                $msg .= 'Price: $' . number_format($price);
            }
            if ($rangeLow && $rangeHigh) {
                $msg .= ' (Range: $' . number_format($rangeLow) . ' - $' . number_format($rangeHigh) . ')';
            }
            return array(
                'success' => true,
                'data' => array(
                    'message' => $msg,
                    'source' => 'rentcast',
                    'comps' => isset($data['comparables']) ? $data['comparables'] : array()
                )
            );
        } elseif ($status_code === 200 && empty($data)) {
            // API key is valid but no data returned
            return array(
                'success' => true,
                'data' => array(
                    'message' => __('RentCast API key is valid, but no property data was returned for this address.', 'georgia-property-tax-calculator'),
                    'source' => 'rentcast',
                    'comps' => array()
                )
            );
        } elseif (isset($data['error'])) {
            return array(
                'success' => false,
                'error' => $data['error']
            );
        } else {
            return array(
                'success' => false,
                'error' => __('Could not retrieve property value from RentCast.', 'georgia-property-tax-calculator')
            );
        }
    }
    
    /**
     * Get comprehensive property data (geocoding + value)
     */
    public function get_property_data($address, $user_provided_value = null, $details = array()) {
        $result = array(
            'success' => false,
            'geocoding' => null,
            'property_value' => null,
            'errors' => array()
        );
        // Step 1: Geocode the address
        $geocoding_result = get_option('gptc_enable_google_maps_api', '1') ? $this->geocode_address($address) : array('success' => false, 'error' => __('Google Maps API is disabled in settings.', 'georgia-property-tax-calculator'));
        $result['geocoding'] = $geocoding_result;
        if (!$geocoding_result['success']) {
            $result['errors'][] = 'Geocoding: ' . $geocoding_result['error'];
        }
        // Step 2: Get property value (if not provided by user)
        if (empty($user_provided_value) && get_option('gptc_enable_rentcast_api', '1')) {
            // Optionally pass property details if available
            $property_details = array();
            if (isset($details['propertyType'])) $property_details['propertyType'] = $details['propertyType'];
            if (isset($details['bedrooms'])) $property_details['bedrooms'] = $details['bedrooms'];
            if (isset($details['bathrooms'])) $property_details['bathrooms'] = $details['bathrooms'];
            if (isset($details['squareFootage'])) $property_details['squareFootage'] = $details['squareFootage'];
            $value_result = $this->get_property_value($address, null, null, $property_details);
            $result['property_value'] = $value_result;
            if (!$value_result['success']) {
                $result['errors'][] = 'Property Value: ' . $value_result['error'];
            }
        } elseif (!empty($user_provided_value)) {
            // Use user-provided value
            $result['property_value'] = array(
                'success' => true,
                'data' => array(
                    'value' => floatval($user_provided_value),
                    'source' => 'user_provided'
                )
            );
        } else {
            $result['property_value'] = array(
                'success' => false,
                'error' => __('RentCast API is disabled in settings and no value provided.', 'georgia-property-tax-calculator')
            );
            $result['errors'][] = 'Property Value: ' . __('RentCast API is disabled in settings and no value provided.', 'georgia-property-tax-calculator');
        }
        // Determine overall success
        $has_county = $geocoding_result['success'] && !empty($geocoding_result['data']['county']);
        $has_value = $result['property_value']['success'];
        $result['success'] = $has_county && $has_value;
        if ($result['success']) {
            $result['data'] = array(
                'address' => $geocoding_result['data']['formatted_address'],
                'county' => $geocoding_result['data']['county'],
                'latitude' => $geocoding_result['data']['latitude'],
                'longitude' => $geocoding_result['data']['longitude'],
                'market_value' => $result['property_value']['data']['value'],
                'value_source' => isset($result['property_value']['data']['source']) ? $result['property_value']['data']['source'] : 'api',
                'comps' => isset($result['property_value']['data']['comps']) ? $result['property_value']['data']['comps'] : array()
            );
        }
        return $result;
    }
    
    /**
     * Test API connections
     */
    public function test_api_connections() {
        $results = array(
            'google_maps' => array('configured' => !empty($this->google_maps_api_key)),
            'rentcast' => array('configured' => !empty($this->rentcast_api_key))
        );
        
        // Test Google Maps API
        if ($results['google_maps']['configured']) {
            $test_geocode = $this->geocode_address('1600 Amphitheatre Parkway, Mountain View, CA');
            $results['google_maps']['working'] = $test_geocode['success'];
            $results['google_maps']['error'] = $test_geocode['success'] ? null : $test_geocode['error'];
        }
        
        // Test RentCast API
        if ($results['rentcast']['configured']) {
            $test_value = $this->get_property_value('1600 Amphitheatre Parkway, Mountain View, CA');
            $results['rentcast']['working'] = $test_value['success'];
            $results['rentcast']['error'] = $test_value['success'] ? null : $test_value['error'];
        }
        
        return $results;
    }
    
    /**
     * Get fallback property value estimate
     */
    public function get_fallback_property_value($county, $property_type = 'home') {
        // Fallback values based on Georgia county averages
        $fallback_values = array(
            'Fulton' => array('home' => 350000, 'commercial' => 750000),
            'DeKalb' => array('home' => 280000, 'commercial' => 650000),
            'Gwinnett' => array('home' => 320000, 'commercial' => 700000),
            'Cobb' => array('home' => 340000, 'commercial' => 720000),
            'Clayton' => array('home' => 180000, 'commercial' => 450000),
            'Cherokee' => array('home' => 290000, 'commercial' => 600000),
            'Henry' => array('home' => 220000, 'commercial' => 500000),
            'Forsyth' => array('home' => 380000, 'commercial' => 800000),
            'Douglas' => array('home' => 200000, 'commercial' => 480000),
            'Fayette' => array('home' => 360000, 'commercial' => 750000)
        );
        
        if (isset($fallback_values[$county][$property_type])) {
            return $fallback_values[$county][$property_type];
        }
        
        // Default fallback
        return $property_type === 'home' ? 250000 : 600000;
    }
}
