<?php

/**
 * Admin functionality for Georgia Property Tax Calculator
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class GPTC_Admin
{

    public function __construct()
    {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'register_settings'));
        add_action('admin_notices', array($this, 'admin_notices'));
        // Register AJAX handler for API test
        add_action('wp_ajax_gptc_test_api', array($this, 'ajax_test_api'));
    }

    /**
     * Add admin menu pages
     */
    public function add_admin_menu()
    {
        // Main menu page
        add_menu_page(
            __('Property Tax Calculator', 'georgia-property-tax-calculator'),
            __('Property Tax Calculator', 'georgia-property-tax-calculator'),
            'manage_options',
            'gptc-admin',
            array($this, 'admin_page'),
            'dashicons-calculator',
            30
        );

        // Settings submenu
        add_submenu_page(
            'gptc-admin',
            __('Settings', 'georgia-property-tax-calculator'),
            __('Settings', 'georgia-property-tax-calculator'),
            'manage_options',
            'gptc-settings',
            array($this, 'settings_page')
        );

        // Submissions submenu
        add_submenu_page(
            'gptc-admin',
            __('Submissions', 'georgia-property-tax-calculator'),
            __('Submissions', 'georgia-property-tax-calculator'),
            'manage_options',
            'gptc-submissions',
            array($this, 'submissions_page')
        );

        // County Data submenu
        add_submenu_page(
            'gptc-admin',
            __('County Data', 'georgia-property-tax-calculator'),
            __('County Data', 'georgia-property-tax-calculator'),
            'manage_options',
            'gptc-counties',
            array($this, 'counties_page')
        );
    }

    /**
     * Register plugin settings
     */
    public function register_settings()
    {
        // API Settings
        register_setting('gptc_api_settings', 'gptc_enable_google_maps_api');
        register_setting('gptc_api_settings', 'gptc_enable_rentcast_api');
        register_setting('gptc_api_settings', 'gptc_google_maps_api_key');
        register_setting('gptc_api_settings', 'gptc_rentcast_api_key');

        // Email Settings
        register_setting('gptc_email_settings', 'gptc_notification_email');
        register_setting('gptc_email_settings', 'gptc_email_from_name');
        register_setting('gptc_email_settings', 'gptc_email_from_email');

        // Company Settings
        register_setting('gptc_company_settings', 'gptc_company_name');
        register_setting('gptc_company_settings', 'gptc_company_phone');
        register_setting('gptc_company_settings', 'gptc_company_logo_url');

        // Dynamic Property Types and States
        register_setting('gptc_api_settings', 'gptc_property_types');
        register_setting('gptc_api_settings', 'gptc_states');

        // Add settings sections
        add_settings_section(
            'gptc_api_section',
            __('API Configuration', 'georgia-property-tax-calculator'),
            array($this, 'api_section_callback'),
            'gptc_api_settings'
        );

        add_settings_section(
            'gptc_email_section',
            __('Email Configuration', 'georgia-property-tax-calculator'),
            array($this, 'email_section_callback'),
            'gptc_email_settings'
        );

        add_settings_section(
            'gptc_company_section',
            __('Company Information', 'georgia-property-tax-calculator'),
            array($this, 'company_section_callback'),
            'gptc_company_settings'
        );

        // Add settings fields
        $this->add_settings_fields();
    }

    /**
     * Add settings fields
     */
    private function add_settings_fields()
    {
        // API Enable/Disable Switches
        add_settings_field(
            'gptc_enable_google_maps_api',
            __('Enable Google Maps API', 'georgia-property-tax-calculator'),
            array($this, 'enable_google_maps_api_callback'),
            'gptc_api_settings',
            'gptc_api_section'
        );
        add_settings_field(
            'gptc_enable_rentcast_api',
            __('Enable RentCast API', 'georgia-property-tax-calculator'),
            array($this, 'enable_rentcast_api_callback'),
            'gptc_api_settings',
            'gptc_api_section'
        );
        // API Fields
        add_settings_field(
            'gptc_google_maps_api_key',
            __('Google Maps API Key', 'georgia-property-tax-calculator'),
            array($this, 'google_maps_api_key_callback'),
            'gptc_api_settings',
            'gptc_api_section'
        );

        add_settings_field(
            'gptc_rentcast_api_key',
            __('RentCast API Key', 'georgia-property-tax-calculator'),
            array($this, 'rentcast_api_key_callback'),
            'gptc_api_settings',
            'gptc_api_section'
        );

        // Dynamic Property Types
        add_settings_field(
            'gptc_property_types',
            __('Property Types', 'georgia-property-tax-calculator'),
            array(
                $this,
                'property_types_callback'
            ),
            'gptc_api_settings',
            'gptc_api_section'
        );
        // Dynamic States
        add_settings_field(
            'gptc_states',
            __('States', 'georgia-property-tax-calculator'),
            array(
                $this,
                'states_callback'
            ),
            'gptc_api_settings',
            'gptc_api_section'
        );

        // Email Fields
        add_settings_field(
            'gptc_notification_email',
            __('Notification Email', 'georgia-property-tax-calculator'),
            array($this, 'notification_email_callback'),
            'gptc_email_settings',
            'gptc_email_section'
        );

        add_settings_field(
            'gptc_email_from_name',
            __('From Name', 'georgia-property-tax-calculator'),
            array($this, 'email_from_name_callback'),
            'gptc_email_settings',
            'gptc_email_section'
        );

        add_settings_field(
            'gptc_email_from_email',
            __('From Email', 'georgia-property-tax-calculator'),
            array($this, 'email_from_email_callback'),
            'gptc_email_settings',
            'gptc_email_section'
        );

        // Company Fields
        add_settings_field(
            'gptc_company_name',
            __('Company Name', 'georgia-property-tax-calculator'),
            array($this, 'company_name_callback'),
            'gptc_company_settings',
            'gptc_company_section'
        );

        add_settings_field(
            'gptc_company_phone',
            __('Company Phone', 'georgia-property-tax-calculator'),
            array($this, 'company_phone_callback'),
            'gptc_company_settings',
            'gptc_company_section'
        );

        add_settings_field(
            'gptc_company_logo_url',
            __('Company Logo URL', 'georgia-property-tax-calculator'),
            array($this, 'company_logo_url_callback'),
            'gptc_company_settings',
            'gptc_company_section'
        );
    }

    /**
     * Main admin page
     */
    public function admin_page()
    {
        $database = new GPTC_Database();
        $submission_count = $database->get_submission_count();
?>
        <style>
        .gptc-dashboard-card {
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 4px 24px rgba(30,58,138,0.08);
            padding: 32px 32px 24px 32px;
            margin: 32px auto 0 auto;
            max-width: 900px;
            width: 100%;
        }
        .gptc-dashboard-card h1 {
            font-size: 2em;
            color: #1E3A8A;
            font-weight: 700;
            margin-bottom: 24px;
        }
        .gptc-stats-grid {
            display: flex;
            gap: 32px;
            margin-bottom: 32px;
            flex-wrap: wrap;
        }
        .gptc-stat-card {
            background: #f8fafc;
            border-radius: 12px;
            box-shadow: 0 1px 4px #1e3a8a08;
            padding: 28px 32px;
            flex: 1 1 220px;
            min-width: 220px;
            text-align: center;
        }
        .gptc-stat-card h3 {
            color: #1E3A8A;
            font-size: 1.1em;
            font-weight: 700;
            margin-bottom: 10px;
        }
        .gptc-stat-number {
            font-size: 2.2em;
            font-weight: 800;
            color: #059669;
            margin-bottom: 0;
        }
        .gptc-quick-links {
            margin-bottom: 32px;
        }
        .gptc-quick-links h3 {
            color: #1E3A8A;
            font-size: 1.1em;
            font-weight: 700;
            margin-bottom: 10px;
        }
        .gptc-quick-links ul {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            gap: 18px;
        }
        .gptc-quick-links li {
            margin: 0;
        }
        .gptc-quick-links a {
            color: #fff;
            background: #1E3A8A;
            padding: 10px 22px;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            transition: background 0.2s;
            display: inline-block;
        }
        .gptc-quick-links a:hover {
            background: #223b7a;
        }
        .gptc-setup-guide {
            background: #f8fafc;
            border-radius: 12px;
            padding: 24px 28px;
            margin-top: 24px;
        }
        .gptc-setup-guide h3 {
            color: #1E3A8A;
            font-size: 1.1em;
            font-weight: 700;
            margin-bottom: 10px;
        }
        .gptc-setup-guide ol {
            margin: 0 0 0 18px;
            padding: 0;
        }
        .gptc-setup-guide li {
            margin-bottom: 8px;
            font-size: 1em;
            color: #222;
        }
        @media (max-width: 900px) {
            .gptc-dashboard-card {
                padding: 16px 4px 12px 4px;
                max-width: 100%;
            }
            .gptc-stats-grid {
                flex-direction: column;
                gap: 18px;
            }
            .gptc-stat-card {
                padding: 18px 12px;
            }
            .gptc-setup-guide {
                padding: 14px 8px;
            }
        }
        </style>
        <div class="gptc-dashboard-card">
            <h1><?php _e('Georgia Property Tax Calculator', 'georgia-property-tax-calculator'); ?></h1>
                <div class="gptc-stats-grid">
                    <div class="gptc-stat-card">
                        <h3><?php _e('Total Submissions', 'georgia-property-tax-calculator'); ?></h3>
                        <div class="gptc-stat-number"><?php echo number_format($submission_count); ?></div>
                    </div>
                    <div class="gptc-stat-card">
                        <h3><?php _e('Shortcode', 'georgia-property-tax-calculator'); ?></h3>
                        <code>[gtp_tax_form]</code>
                        <p><?php _e('Use this shortcode to display the calculator on any page or post.', 'georgia-property-tax-calculator'); ?></p>
                    </div>
                </div>
                <div class="gptc-quick-links">
                    <h3><?php _e('Quick Links', 'georgia-property-tax-calculator'); ?></h3>
                    <ul>
                        <li><a href="<?php echo admin_url('admin.php?page=gptc-settings'); ?>"><?php _e('Configure API Keys', 'georgia-property-tax-calculator'); ?></a></li>
                        <li><a href="<?php echo admin_url('admin.php?page=gptc-submissions'); ?>"><?php _e('View Submissions', 'georgia-property-tax-calculator'); ?></a></li>
                        <li><a href="<?php echo admin_url('admin.php?page=gptc-counties'); ?>"><?php _e('Manage County Data', 'georgia-property-tax-calculator'); ?></a></li>
                    </ul>
                </div>
                <div class="gptc-setup-guide">
                    <h3><?php _e('Setup Guide', 'georgia-property-tax-calculator'); ?></h3>
                    <ol>
                        <li><?php _e('Configure your Google Maps API key for address geocoding', 'georgia-property-tax-calculator'); ?></li>
                        <li><?php _e('Set up your RentCast API key for property value lookup', 'georgia-property-tax-calculator'); ?></li>
                        <li><?php _e('Configure email settings for notifications', 'georgia-property-tax-calculator'); ?></li>
                        <li><?php _e('Add the shortcode [gtp_tax_form] to any page', 'georgia-property-tax-calculator'); ?></li>
                    </ol>
            </div>
        </div>
    <?php
    }

    /**
     * Settings page
     */
    public function settings_page()
    {
    ?>
        <style>
        .gptc-settings-card {
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 4px 24px rgba(30,58,138,0.08);
            padding: 32px 32px 24px 32px;
            margin: 32px auto 0 auto;
            max-width: 700px;
            width: 100%;
        }
        .gptc-settings-card h1 {
            font-size: 1.7em;
            color: #1E3A8A;
            font-weight: 700;
            margin-bottom: 24px;
        }
        .gptc-settings-tabs {
            display: flex;
            gap: 0;
            margin-bottom: 24px;
            border-bottom: 2px solid #e5e7eb;
        }
        .gptc-settings-tab {
            padding: 12px 32px;
            background: #f8fafc;
            color: #1E3A8A;
            font-weight: 600;
            font-size: 1.1em;
            border: none;
            border-radius: 12px 12px 0 0;
            margin-right: 8px;
            cursor: pointer;
            transition: background 0.2s, color 0.2s;
        }
        .gptc-settings-tab.gptc-active {
            background: #1E3A8A;
            color: #fff;
        }
        .gptc-settings-section {
            margin-bottom: 32px;
        }
        .gptc-settings-section h2 {
            color: #1E3A8A;
            font-size: 1.2em;
            font-weight: 700;
            margin-bottom: 18px;
        }
        .gptc-settings-section form {
            margin-bottom: 0;
        }
        .gptc-settings-section .form-table th {
            color: #1E3A8A;
            font-weight: 600;
            font-size: 1em;
            padding: 10px 8px 10px 0;
        }
        .gptc-settings-section .form-table td {
            padding: 10px 0 10px 0;
        }
        .gptc-settings-section input[type="text"],
        .gptc-settings-section input[type="email"],
        .gptc-settings-section input[type="url"],
        .gptc-settings-section input[type="password"],
        .gptc-settings-section textarea {
            width: 100%;
            max-width: 400px;
            padding: 10px 14px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 15px;
            background: #f8fafc;
            transition: border-color 0.2s;
        }
        .gptc-settings-section input:focus,
        .gptc-settings-section textarea:focus {
            border-color: #1E3A8A;
            background: #fff;
            outline: none;
        }
        .gptc-settings-section .button-primary {
            background: #1E3A8A;
            color: #fff;
            font-weight: 700;
            border-radius: 8px;
            padding: 12px 32px;
            font-size: 1.1em;
            margin-top: 18px;
            box-shadow: 0 2px 8px #1e3a8a11;
            border: none;
            transition: background 0.2s, color 0.2s;
        }
        .gptc-settings-section .button-primary:hover {
            background: #223b7a;
            color: #fff;
        }
        @media (max-width: 800px) {
            .gptc-settings-card {
                padding: 16px 4px 12px 4px;
                max-width: 100%;
            }
            .gptc-settings-tabs {
                flex-direction: column;
                gap: 8px;
            }
            .gptc-settings-tab {
                width: 100%;
                border-radius: 12px 12px 0 0;
                margin-right: 0;
            }
        }
        </style>
        <div class="gptc-settings-card">
            <h1><?php _e('Property Tax Calculator Settings', 'georgia-property-tax-calculator'); ?></h1>
            <div class="gptc-settings-tabs">
                <button type="button" class="gptc-settings-tab gptc-active" data-tab="#api-settings"><?php _e('API Settings', 'georgia-property-tax-calculator'); ?></button>
                <button type="button" class="gptc-settings-tab" data-tab="#email-settings"><?php _e('Email Settings', 'georgia-property-tax-calculator'); ?></button>
                <button type="button" class="gptc-settings-tab" data-tab="#company-settings"><?php _e('Company Settings', 'georgia-property-tax-calculator'); ?></button>
            </div>
            <div id="api-settings" class="gptc-settings-section tab-content">
                <button type="button" class="button gptc-button-primary" id="gptc-api-instructions-btn" style="margin-bottom: 15px;">
                    <?php _e('API Configuration Instructions', 'georgia-property-tax-calculator'); ?>
                </button>
                <!-- Add nonce field for AJAX security -->
                <input type="hidden" id="_gptc_nonce" value="<?php echo esc_attr(wp_create_nonce('gptc_nonce')); ?>" />
                <form method="post" action="options.php">
                    <?php
                    settings_fields('gptc_api_settings');
                    do_settings_sections('gptc_api_settings');
                    submit_button();
                    ?>
                </form>
                <!-- Modal markup -->
                <div id="gptc-api-instructions-modal" style="display:none; position:fixed; z-index:9999; left:0; top:0; width:100vw; height:100vh; background:rgba(0,0,0,0.4);">
                    <div style="background:#fff; max-width:600px; margin:60px auto; padding:30px 30px 20px 30px; border-radius:8px; position:relative; box-shadow:0 2px 16px rgba(0,0,0,0.2);">
                        <button type="button" id="gptc-api-instructions-close" style="position:absolute; top:10px; right:10px; background:none; border:none; font-size:22px; cursor:pointer;">&times;</button>
                        <h2 style="margin-top:0;"><?php _e('API Configuration Instructions', 'georgia-property-tax-calculator'); ?></h2>
                        <h3><?php _e('Google Maps API Key', 'georgia-property-tax-calculator'); ?></h3>
                        <ol>
                            <li><?php _e('Go to', 'georgia-property-tax-calculator'); ?> <a href="https://console.cloud.google.com" target="_blank" rel="noopener">Google Cloud Console</a>.</li>
                            <li><?php _e('Create a new project or select an existing one.', 'georgia-property-tax-calculator'); ?></li>
                            <li><?php _e('Enable the Geocoding API for your project.', 'georgia-property-tax-calculator'); ?></li>
                            <li><?php _e('Navigate to "APIs & Services > Credentials" and click "Create Credentials > API key".', 'georgia-property-tax-calculator'); ?></li>
                            <li><?php _e('Restrict your API key to your domain for security.', 'georgia-property-tax-calculator'); ?></li>
                            <li><?php _e('Copy the API key and paste it in the field above.', 'georgia-property-tax-calculator'); ?></li>
                        </ol>
                        <h3><?php _e('RentCast API Key', 'georgia-property-tax-calculator'); ?></h3>
                        <ol>
                            <li><?php _e('Visit', 'georgia-property-tax-calculator'); ?> <a href="https://www.rentcast.io" target="_blank" rel="noopener">RentCast.io</a>.</li>
                            <li><?php _e('Sign up for an account or log in.', 'georgia-property-tax-calculator'); ?></li>
                            <li><?php _e('Go to your dashboard and generate an API key.', 'georgia-property-tax-calculator'); ?></li>
                            <li><?php _e('Copy the API key and paste it in the field above.', 'georgia-property-tax-calculator'); ?></li>
                        </ol>
                        <p style="margin-top:20px;"><strong><?php _e('Need help?', 'georgia-property-tax-calculator'); ?></strong> <?php _e('Contact support at', 'georgia-property-tax-calculator'); ?> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    </div>
                </div>
            </div>
            <div id="email-settings" class="gptc-settings-section tab-content" style="display:none;">
                <form method="post" action="options.php">
                    <?php
                    settings_fields('gptc_email_settings');
                    do_settings_sections('gptc_email_settings');
                    submit_button();
                    ?>
                </form>
            </div>
            <div id="company-settings" class="gptc-settings-section tab-content" style="display:none;">
                <form method="post" action="options.php">
                    <?php
                    settings_fields('gptc_company_settings');
                    do_settings_sections('gptc_company_settings');
                    submit_button();
                    ?>
                </form>
            </div>
        </div>
        <script>
            jQuery(document).ready(function($) {
                $('.gptc-settings-tab').click(function(e) {
                    e.preventDefault();
                    $('.gptc-settings-tab').removeClass('gptc-active');
                    $(this).addClass('gptc-active');
                    $('.tab-content').hide();
                    $($(this).data('tab')).show();
                });
                // Modal logic
                $('#gptc-api-instructions-btn').on('click', function() {
                    $('#gptc-api-instructions-modal').fadeIn(150);
                });
                $('#gptc-api-instructions-close, #gptc-api-instructions-modal').on('click', function(e) {
                    if (e.target === this) {
                        $('#gptc-api-instructions-modal').fadeOut(150);
                    }
                });
                $('#gptc-api-instructions-modal > div').on('click', function(e) {
                    e.stopPropagation();
                });
            });
        </script>
    <?php
    }

    // Section callbacks
    public function api_section_callback()
    {
        echo '<p>' . __('Configure your API keys for Google Maps and RentCast integration.', 'georgia-property-tax-calculator') . '</p>';
    }

    public function email_section_callback()
    {
        echo '<p>' . __('Configure email settings for notifications.', 'georgia-property-tax-calculator') . '</p>';
    }

    public function company_section_callback()
    {
        echo '<p>' . __('Configure your company information for emails and branding.', 'georgia-property-tax-calculator') . '</p>';
    }

    // Field callbacks
    public function enable_google_maps_api_callback()
    {
        $enabled = get_option('gptc_enable_google_maps_api', '1');
        echo '<input type="checkbox" name="gptc_enable_google_maps_api" value="1"' . checked('1', $enabled, false) . ' /> ';
        echo '<span class="description">' . __('Use Google Maps API for address geocoding and county detection.', 'georgia-property-tax-calculator') . '</span>';
    }
    public function enable_rentcast_api_callback()
    {
        $enabled = get_option('gptc_enable_rentcast_api', '1');
        echo '<input type="checkbox" name="gptc_enable_rentcast_api" value="1"' . checked('1', $enabled, false) . ' /> ';
        echo '<span class="description">' . __('Use RentCast API for property value lookup.', 'georgia-property-tax-calculator') . '</span>';
    }

    public function google_maps_api_key_callback()
    {
        $value = get_option('gptc_google_maps_api_key', '');
        echo '<input type="password" name="gptc_google_maps_api_key" value="' . esc_attr($value) . '" class="regular-text" />';
        echo '<p class="description">' . __('Get your API key from Google Cloud Console', 'georgia-property-tax-calculator') . '</p>';
    }

    public function rentcast_api_key_callback()
    {
        $value = get_option('gptc_rentcast_api_key', '');
        echo '<input type="password" name="gptc_rentcast_api_key" value="' . esc_attr($value) . '" class="regular-text" />';
        echo '<p class="description">' . __('Get your API key from RentCast.io', 'georgia-property-tax-calculator') . '</p>';
    }

    public function property_types_callback()
    {
        $value = get_option('gptc_property_types', json_encode(array('home' => 'Home', 'commercial' => 'Commercial')));
        $types = json_decode($value, true);
        if (!is_array($types)) {
            $types = array('home' => 'Home', 'commercial' => 'Commercial');
        }
        echo '<div id="gptc-property-types-repeater">';
        foreach ($types as $key => $label) {
            echo '<div class="gptc-repeater-row" style="display:flex; gap:8px; margin-bottom:6px;">';
            echo '<input type="text" name="gptc_property_types_keys[]" value="' . esc_attr($key) . '" placeholder="Key (e.g. home)" style="width:120px;" required />';
            echo '<input type="text" name="gptc_property_types_labels[]" value="' . esc_attr($label) . '" placeholder="Label (e.g. Home)" style="width:200px;" required />';
            echo '<button type="button" class="button gptc-remove-row" style="color:#b91c1c;">&times;</button>';
            echo '</div>';
        }
        echo '</div>';
        echo '<button type="button" class="button" id="gptc-add-property-type">+ Add Property Type</button>';
        echo '<input type="hidden" name="gptc_property_types" id="gptc_property_types_json" value="' . esc_attr($value) . '" />';
        echo '<p class="description">' . __('Add, edit, or remove property types. Key is the value sent to backend, label is what users see.', 'georgia-property-tax-calculator') . '</p>';
        ?>
        <script>
        jQuery(document).ready(function($){
            $('#gptc-add-property-type').on('click', function(){
                $('#gptc-property-types-repeater').append('<div class="gptc-repeater-row" style="display:flex; gap:8px; margin-bottom:6px;"><input type="text" name="gptc_property_types_keys[]" placeholder="Key (e.g. home)" style="width:120px;" required /><input type="text" name="gptc_property_types_labels[]" placeholder="Label (e.g. Home)" style="width:200px;" required /><button type="button" class="button gptc-remove-row" style="color:#b91c1c;">&times;</button></div>');
            });
            $(document).on('click', '.gptc-remove-row', function(){
                $(this).closest('.gptc-repeater-row').remove();
            });
            // On form submit, serialize to JSON
            $('#gptc_api_settings').closest('form').on('submit', function(){
                var keys = $('input[name="gptc_property_types_keys[]"]').map(function(){return $(this).val();}).get();
                var labels = $('input[name="gptc_property_types_labels[]"]').map(function(){return $(this).val();}).get();
                var obj = {};
                for(var i=0; i<keys.length; i++){
                    if(keys[i]) obj[keys[i]] = labels[i];
                }
                $('#gptc_property_types_json').val(JSON.stringify(obj));
            });
        });
        </script>
        <?php
    }
    public function states_callback()
    {
        $value = get_option('gptc_states', json_encode(array('GA' => 'Georgia (GA)')));
        $states = json_decode($value, true);
        if (!is_array($states)) {
            $states = array('GA' => 'Georgia (GA)');
        }
        echo '<div id="gptc-states-repeater">';
        foreach ($states as $key => $label) {
            echo '<div class="gptc-repeater-row" style="display:flex; gap:8px; margin-bottom:6px;">';
            echo '<input type="text" name="gptc_states_keys[]" value="' . esc_attr($key) . '" placeholder="Key (e.g. GA)" style="width:80px;" required />';
            echo '<input type="text" name="gptc_states_labels[]" value="' . esc_attr($label) . '" placeholder="Label (e.g. Georgia (GA))" style="width:200px;" required />';
            echo '<button type="button" class="button gptc-remove-row" style="color:#b91c1c;">&times;</button>';
            echo '</div>';
        }
        echo '</div>';
        echo '<button type="button" class="button" id="gptc-add-state">+ Add State</button>';
        echo '<input type="hidden" name="gptc_states" id="gptc_states_json" value="' . esc_attr($value) . '" />';
        echo '<p class="description">' . __('Add, edit, or remove states. Key is the value sent to backend, label is what users see.', 'georgia-property-tax-calculator') . '</p>';
        ?>
        <script>
        jQuery(document).ready(function($){
            $('#gptc-add-state').on('click', function(){
                $('#gptc-states-repeater').append('<div class="gptc-repeater-row" style="display:flex; gap:8px; margin-bottom:6px;"><input type="text" name="gptc_states_keys[]" placeholder="Key (e.g. GA)" style="width:80px;" required /><input type="text" name="gptc_states_labels[]" placeholder="Label (e.g. Georgia (GA))" style="width:200px;" required /><button type="button" class="button gptc-remove-row" style="color:#b91c1c;">&times;</button></div>');
            });
            $(document).on('click', '.gptc-remove-row', function(){
                $(this).closest('.gptc-repeater-row').remove();
            });
            // On form submit, serialize to JSON
            $('#gptc_api_settings').closest('form').on('submit', function(){
                var keys = $('input[name="gptc_states_keys[]"]').map(function(){return $(this).val();}).get();
                var labels = $('input[name="gptc_states_labels[]"]').map(function(){return $(this).val();}).get();
                var obj = {};
                for(var i=0; i<keys.length; i++){
                    if(keys[i]) obj[keys[i]] = labels[i];
                }
                $('#gptc_states_json').val(JSON.stringify(obj));
            });
        });
        </script>
        <?php
    }

    public function notification_email_callback()
    {
        $value = get_option('gptc_notification_email', '<EMAIL>');
        echo '<input type="email" name="gptc_notification_email" value="' . esc_attr($value) . '" class="regular-text" />';
    }

    public function email_from_name_callback()
    {
        $value = get_option('gptc_email_from_name', 'Property Tax Reports USA');
        echo '<input type="text" name="gptc_email_from_name" value="' . esc_attr($value) . '" class="regular-text" />';
    }

    public function email_from_email_callback()
    {
        $value = get_option('gptc_email_from_email', '<EMAIL>');
        echo '<input type="email" name="gptc_email_from_email" value="' . esc_attr($value) . '" class="regular-text" />';
    }

    public function company_name_callback()
    {
        $value = get_option('gptc_company_name', 'Property Tax Reports USA');
        echo '<input type="text" name="gptc_company_name" value="' . esc_attr($value) . '" class="regular-text" />';
    }

    public function company_phone_callback()
    {
        $value = get_option('gptc_company_phone', '************');
        echo '<input type="text" name="gptc_company_phone" value="' . esc_attr($value) . '" class="regular-text" />';
    }

    public function company_logo_url_callback()
    {
        $value = get_option('gptc_company_logo_url', '');
        echo '<input type="url" name="gptc_company_logo_url" value="' . esc_attr($value) . '" class="regular-text" />';
        echo '<p class="description">' . __('URL to your company logo for emails', 'georgia-property-tax-calculator') . '</p>';
    }

    /**
     * AJAX handler for API key testing
     */
    public function ajax_test_api()
    {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'gptc_nonce')) {
            wp_send_json_error(__('Security check failed.', 'georgia-property-tax-calculator'), 400);
        }
        // Check user capability
        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Unauthorized.', 'georgia-property-tax-calculator'), 403);
        }
        $api_type = isset($_POST['api_type']) ? sanitize_text_field($_POST['api_type']) : '';
        $api_key = isset($_POST['api_key']) ? sanitize_text_field($_POST['api_key']) : '';
        if (!$api_type || !$api_key) {
            wp_send_json_error(__('Missing API type or key.', 'georgia-property-tax-calculator'), 400);
        }
        if ($api_type === 'google_maps') {
            if (!get_option('gptc_enable_google_maps_api', '1')) {
                wp_send_json_error(__('Google Maps API is disabled in settings.', 'georgia-property-tax-calculator'));
            }
            // Test Google Maps Geocoding API
            $address = urlencode('1600 Amphitheatre Parkway, Mountain View, CA');
            $url = "https://maps.googleapis.com/maps/api/geocode/json?address={$address}&key={$api_key}";
            $response = wp_remote_get($url, array('timeout' => 15));
            if (is_wp_error($response)) {
                wp_send_json_error($response->get_error_message());
            }
            $body = wp_remote_retrieve_body($response);
            $data = json_decode($body, true);
            if (isset($data['status']) && $data['status'] === 'OK') {
                wp_send_json_success(__('Google Maps API key is valid.', 'georgia-property-tax-calculator'));
            } else {
                $error = isset($data['error_message']) ? $data['error_message'] : __('Invalid API key or error from Google Maps.', 'georgia-property-tax-calculator');
                wp_send_json_error($error);
            }
        } elseif ($api_type === 'rentcast') {
            if (!get_option('gptc_enable_rentcast_api', '1')) {
                wp_send_json_error(__('RentCast API is disabled in settings.', 'georgia-property-tax-calculator'));
            }
            // Test RentCast API with the official /v1/avm/value endpoint
            $test_address = '401 Sarah Ln, Lawrenceville, GA 30046';
            $url = 'https://api.rentcast.io/v1/avm/value?address=' . urlencode($test_address);
            $response = wp_remote_get($url, array(
                'timeout' => 15,
                'headers' => array('X-Api-Key' => $api_key, 'Accept' => 'application/json')
            ));
            if (is_wp_error($response)) {
                wp_send_json_error($response->get_error_message());
            }
            $body = wp_remote_retrieve_body($response);
            $data = json_decode($body, true);
            if (isset($data['valueEstimate'])) {
                wp_send_json_success(__('RentCast API key is valid. Value estimate: ', 'georgia-property-tax-calculator') . $data['valueEstimate']);
            } elseif (isset($data['price']) || (isset($data['priceRangeLow']) && isset($data['priceRangeHigh']))) {
                // Handle cases where the API returns 'price' and/or price range instead of 'valueEstimate'
                $price = isset($data['price']) ? $data['price'] : null;
                $rangeLow = isset($data['priceRangeLow']) ? $data['priceRangeLow'] : null;
                $rangeHigh = isset($data['priceRangeHigh']) ? $data['priceRangeHigh'] : null;
                $msg = __('RentCast API key is valid. ', 'georgia-property-tax-calculator');
                if ($price) {
                    $msg .= 'Price: $' . number_format($price);
                }
                if ($rangeLow && $rangeHigh) {
                    $msg .= ' (Range: $' . number_format($rangeLow) . ' - $' . number_format($rangeHigh) . ')';
                }
                wp_send_json_success($msg);
            } elseif (isset($data['error'])) {
                // Return the full error message for debugging
                $error_message = is_array($data['error']) ? json_encode($data['error']) : $data['error'];
                $extra_message = isset($data['message']) ? ' - ' . $data['message'] : '';
                wp_send_json_error('RentCast API error: ' . $error_message . $extra_message);
            } else {
                // Log the full response for debugging
                wp_send_json_error('Unknown error. Full response: ' . json_encode($data));
            }
        } else {
            wp_send_json_error(__('Unknown API type.', 'georgia-property-tax-calculator'), 400);
        }
    }

    /**
     * Submissions page
     */
    public function submissions_page()
    {
        $database = new GPTC_Database();
        $submissions = $database->get_submissions(50, 0);
    ?>
        <style>
        .gptc-admin-submissions-card {
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 4px 24px rgba(30,58,138,0.08);
            padding: 32px 32px 24px 32px;
            margin: 32px auto 0 auto;
            max-width: 1100px;
            width: 100%;
        }
        .gptc-admin-submissions-card h1 {
            font-size: 1.7em;
            color: #1E3A8A;
            font-weight: 700;
            margin-bottom: 24px;
        }
        .gptc-admin-submissions-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            background: #f8fafc;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 4px #1e3a8a08;
        }
        .gptc-admin-submissions-table th {
            background: #1E3A8A;
            color: #fff;
            font-weight: 700;
            padding: 14px 10px;
            font-size: 1em;
            position: sticky;
            top: 0;
            z-index: 2;
        }
        .gptc-admin-submissions-table td {
            padding: 13px 10px;
            font-size: 1em;
            color: #222;
            background: #fff;
            border-bottom: 1px solid #e5e7eb;
        }
        .gptc-admin-submissions-table tr:nth-child(even) td {
            background: #f3f6fa;
        }
        .gptc-admin-submissions-table tr:hover td {
            background: #e0e7ff;
        }
        .gptc-admin-submissions-table td, .gptc-admin-submissions-table th {
            text-align: left;
        }
        @media (max-width: 900px) {
            .gptc-admin-submissions-card {
                padding: 16px 4px 12px 4px;
                max-width: 100%;
            }
            .gptc-admin-submissions-table th, .gptc-admin-submissions-table td {
                font-size: 0.95em;
                padding: 8px 4px;
            }
        }
        </style>
        <div class="gptc-admin-submissions-card">
            <h1><?php _e('Form Submissions', 'georgia-property-tax-calculator'); ?></h1>
            <table class="gptc-admin-submissions-table">
                <thead>
                    <tr>
                        <th><?php _e('Date', 'georgia-property-tax-calculator'); ?></th>
                        <th><?php _e('Name', 'georgia-property-tax-calculator'); ?></th>
                        <th><?php _e('Email', 'georgia-property-tax-calculator'); ?></th>
                        <th><?php _e('Address', 'georgia-property-tax-calculator'); ?></th>
                        <th><?php _e('County', 'georgia-property-tax-calculator'); ?></th>
                        <th><?php _e('Market Value', 'georgia-property-tax-calculator'); ?></th>
                        <th><?php _e('Annual Tax', 'georgia-property-tax-calculator'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($submissions)): ?>
                        <tr>
                            <td colspan="7"><?php _e('No submissions found.', 'georgia-property-tax-calculator'); ?></td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($submissions as $submission): ?>
                            <tr>
                                <td><?php echo esc_html(date('M j, Y g:i A', strtotime($submission['submission_date']))); ?></td>
                                <td><?php echo esc_html($submission['name']); ?></td>
                                <td><?php echo esc_html($submission['email']); ?></td>
                                <td><?php echo esc_html($submission['address']); ?></td>
                                <td><?php echo esc_html($submission['county']); ?></td>
                                <td><?php echo $submission['market_value'] ? '$' . number_format($submission['market_value']) : '-'; ?></td>
                                <td><?php echo $submission['annual_tax'] ? '$' . number_format($submission['annual_tax']) : '-'; ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
<?php
    }

    /**
     * Counties page
     */
    public function counties_page()
    {
        $database = new GPTC_Database();
        // Handle repair table action
        if (isset($_POST['gptc_repair_county_table']) && current_user_can('manage_options')) {
            $database->create_tables();
            echo '<div class="notice notice-success is-dismissible"><p>' . __('County table repaired and default data restored.', 'georgia-property-tax-calculator') . '</p></div>';
        }
        $counties = $database->get_all_counties('GA');
        // Handle add/edit form submission
        if (isset($_POST['gptc_county_submit']) && current_user_can('manage_options')) {
            $county_name = sanitize_text_field($_POST['county_name']);
            $millage_rate = floatval($_POST['millage_rate']);
            $homestead_exemption = floatval($_POST['homestead_exemption']);
            $senior_exemption = floatval($_POST['senior_exemption']);
            $disabled_exemption = floatval($_POST['disabled_exemption']);
            $assessment_ratio = floatval($_POST['assessment_ratio']);
            $database->update_county_data($county_name, 'GA', array(
                'millage_rate' => $millage_rate,
                'homestead_exemption' => $homestead_exemption,
                'senior_exemption' => $senior_exemption,
                'disabled_exemption' => $disabled_exemption,
                'assessment_ratio' => $assessment_ratio
            ));
            echo '<div class="notice notice-success is-dismissible"><p>' . __('County data updated.', 'georgia-property-tax-calculator') . '</p></div>';
            // Refresh counties
            $counties = $database->get_all_counties('GA');
        }
        // Handle add new county form submission
        if (isset($_POST['gptc_add_county_submit']) && current_user_can('manage_options')) {
            $county_name = sanitize_text_field($_POST['add_county_name']);
            $millage_rate = floatval($_POST['add_millage_rate']);
            $homestead_exemption = floatval($_POST['add_homestead_exemption']);
            $senior_exemption = floatval($_POST['add_senior_exemption']);
            $disabled_exemption = floatval($_POST['add_disabled_exemption']);
            $assessment_ratio = floatval($_POST['add_assessment_ratio']);
            $database->add_county_data(array(
                'county_name' => $county_name,
                'state' => 'GA',
                'millage_rate' => $millage_rate,
                'homestead_exemption' => $homestead_exemption,
                'senior_exemption' => $senior_exemption,
                'disabled_exemption' => $disabled_exemption,
                'assessment_ratio' => $assessment_ratio
            ));
            echo '<div class="notice notice-success is-dismissible"><p>' . __('New county added.', 'georgia-property-tax-calculator') . '</p></div>';
            $counties = $database->get_all_counties('GA');
        }
        // Handle delete county action
        if (isset($_POST['gptc_delete_county']) && current_user_can('manage_options')) {
            $database->delete_county_data(sanitize_text_field($_POST['delete_county_name']), 'GA');
            echo '<div class="notice notice-success is-dismissible"><p>' . __('County deleted.', 'georgia-property-tax-calculator') . '</p></div>';
            $counties = $database->get_all_counties('GA');
        }
        echo '<div class="wrap gptc-county-admin-modern">';
        echo '<h1 style="margin-bottom: 0.5em;">' . __('County Tax Data', 'georgia-property-tax-calculator') . '</h1>';
        echo '<p style="margin-bottom: 2em; color: #555; font-size: 1.1em;">' . __('Manage Georgia county tax rates and exemptions. Add, edit, or delete counties as needed.', 'georgia-property-tax-calculator') . '</p>';
        // Remove Repair County Table button
        ?>
        <style>
        .gptc-county-card-modern {
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 4px 24px rgba(30,58,138,0.08);
            padding: 32px 32px 24px 32px;
            margin: 32px auto 0 auto;
            max-width: 900px;
            width: 100%;
        }
        .gptc-county-card-modern h1 {
            font-size: 2em;
            color: #1E3A8A;
            font-weight: 700;
            margin-bottom: 24px;
        }
        .gptc-county-table-card {
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 4px 24px rgba(30,58,138,0.08);
            padding: 32px 32px 24px 32px;
            margin-bottom: 32px;
        }
        .gptc-county-table-card h2 {
            color: #1E3A8A;
            font-size: 1.2em;
            font-weight: 700;
            margin-bottom: 18px;
        }
        .gptc-county-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            background: #fff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 4px #1e3a8a08;
            font-size: 1em;
        }
        .gptc-county-table th {
            background: #1E3A8A;
            color: #fff;
            font-weight: 700;
            padding: 14px 10px;
            font-size: 1em;
            position: sticky;
            top: 0;
            z-index: 2;
            border-right: 1px solid #233876;
        }
        .gptc-county-table th:last-child {
            border-right: none;
        }
        .gptc-county-table td {
            padding: 13px 10px;
            font-size: 1em;
            color: #222;
            background: #fff;
            border-bottom: 1px solid #e5e7eb;
        }
        .gptc-county-table tr:nth-child(even) td {
            background: #f3f6fa;
        }
        .gptc-county-table tr:hover td {
            background: #e0e7ff;
        }
        .gptc-county-table td, .gptc-county-table th {
            text-align: left;
        }
        .gptc-county-table td {
            border-right: 1px solid #f1f5f9;
        }
        .gptc-county-table td:last-child {
            border-right: none;
        }
        .gptc-county-table {
            border: 1.5px solid #e5e7eb;
        }
        .gptc-county-table-card > div[style*="overflow-x:auto"] {
            border-radius: 12px;
            overflow-x: auto;
            background: #fff;
        }
        .gptc-county-edit-form, .gptc-county-add-form {
            background: #f8fafc;
            border-radius: 12px;
            box-shadow: 0 1px 4px #1e3a8a08;
            padding: 24px 18px;
            margin-bottom: 32px;
            max-width: 600px;
        }
        .gptc-county-edit-form h2, .gptc-county-add-form h2 {
            color: #1E3A8A;
            font-size: 1.1em;
            font-weight: 700;
            margin-bottom: 18px;
        }
        .gptc-county-edit-form input, .gptc-county-add-form input {
            width: 100%;
            max-width: 300px;
            padding: 10px 14px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 15px;
            background: #fff;
            margin-bottom: 10px;
            transition: border-color 0.2s;
        }
        .gptc-county-edit-form input:focus, .gptc-county-add-form input:focus {
            border-color: #1E3A8A;
            outline: none;
        }
        .gptc-county-edit-form .button-primary, .gptc-county-add-form .button-primary {
            background: #1E3A8A;
            color: #fff;
            font-weight: 700;
            border-radius: 8px;
            padding: 12px 32px;
            font-size: 1.1em;
            margin-top: 10px;
            box-shadow: 0 2px 8px #1e3a8a11;
            border: none;
            transition: background 0.2s, color 0.2s;
        }
        .gptc-county-edit-form .button-primary:hover, .gptc-county-add-form .button-primary:hover {
            background: #223b7a;
            color: #fff;
        }
        .gptc-county-repair-btn {
            margin-bottom: 18px;
        }
        @media (max-width: 900px) {
            .gptc-county-card-modern {
                padding: 16px 4px 12px 4px;
                max-width: 100%;
            }
            .gptc-county-table-card, .gptc-county-edit-form, .gptc-county-add-form {
                padding: 14px 8px;
            }
        }
        </style>
        <div class="gptc-county-card-modern">
            <h1><?php _e('County Tax Data', 'georgia-property-tax-calculator'); ?></h1>
            <div class="gptc-county-table-card">
                <div style="display:flex; justify-content:flex-end; margin-bottom:18px;"><button type="button" class="button button-primary" id="gptc-add-county-btn">+ Add New County</button></div>
                <h2><?php _e('All Georgia Counties', 'georgia-property-tax-calculator'); ?></h2>
                <div style="overflow-x:auto;">
                <table class="gptc-county-table">
                    <thead>
                        <tr>
                            <th><?php _e('County', 'georgia-property-tax-calculator'); ?></th>
                            <th><?php _e('Millage Rate', 'georgia-property-tax-calculator'); ?></th>
                            <th><?php _e('Homestead Exemption', 'georgia-property-tax-calculator'); ?></th>
                            <th><?php _e('Senior Exemption', 'georgia-property-tax-calculator'); ?></th>
                            <th><?php _e('Disabled Exemption', 'georgia-property-tax-calculator'); ?></th>
                            <th><?php _e('Assessment Ratio', 'georgia-property-tax-calculator'); ?></th>
                            <th colspan="2" style="text-align:center;"><?php _e('Actions', 'georgia-property-tax-calculator'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
        <?php
        // Pagination logic
        $per_page = 10;
        $total_counties = count($counties);
        $total_pages = ceil($total_counties / $per_page);
        $current_page = isset($_GET['county_page']) ? max(1, intval($_GET['county_page'])) : 1;
        $start = ($current_page - 1) * $per_page;
        $paged_counties = array_slice($counties, $start, $per_page);
        foreach ($paged_counties as $county) {
            echo '<tr style="vertical-align:middle;">';
            echo '<td style="font-weight:600; color:#1d2327;">' . esc_html($county['county_name']) . '</td>';
            echo '<td>' . esc_html($county['millage_rate']) . '</td>';
            echo '<td>' . esc_html($county['homestead_exemption']) . '</td>';
            echo '<td>' . esc_html($county['senior_exemption']) . '</td>';
            echo '<td>' . esc_html($county['disabled_exemption']) . '</td>';
            echo '<td>' . esc_html($county['assessment_ratio']) . '</td>';
            echo '<td style="text-align:center;"><a href="#" class="gptc-edit-county button button-small" style="margin-right:8px;" data-county="' . esc_attr($county['county_name']) . '" data-millage="' . esc_attr($county['millage_rate']) . '" data-homestead="' . esc_attr($county['homestead_exemption']) . '" data-senior="' . esc_attr($county['senior_exemption']) . '" data-disabled="' . esc_attr($county['disabled_exemption']) . '" data-assessment="' . esc_attr($county['assessment_ratio']) . '">' . __('Edit', 'georgia-property-tax-calculator') . '</a></td>';
            echo '<td style="text-align:center;"><a href="#" class="gptc-delete-county button button-small button-danger" style="color:#b91c1c; background:#fee2e2; border-color:#fca5a5;" data-county="' . esc_attr($county['county_name']) . '">' . __('Delete', 'georgia-property-tax-calculator') . '</a></td>';
            echo '</tr>';
        }
        ?>
                    </tbody>
                </table>
                </div>
                <?php if ($total_pages > 1): ?>
                <div style="display:flex; justify-content:center; margin-top:18px; gap:8px;">
                    <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                        <a href="<?php echo esc_url(add_query_arg('county_page', $i)); ?>" class="button<?php echo $i === $current_page ? ' button-primary' : ' button-secondary'; ?>"><?php echo $i; ?></a>
                    <?php endfor; ?>
                </div>
                <?php endif; ?>
            </div>
        <?php
        // Add New County Button and Modal
        ?>
        <div id="gptc-add-county-modal" class="gptc-modal" style="display:none; position:fixed; z-index:99999; left:0; top:0; width:100vw; height:100vh; background:rgba(30,58,138,0.18);">
            <div class="gptc-modal-card" style="background:#fff; border-radius:16px; box-shadow:0 8px 40px rgba(30,58,138,0.18); padding:36px 36px 28px 36px; max-width:520px; margin:60px auto; position:relative;">
                <button type="button" class="gptc-modal-close" style="position:absolute; top:16px; right:16px; background:#f3f6fa; color:#1E3A8A; border:none; font-size:2em; border-radius:50%; width:40px; height:40px; line-height:40px; text-align:center; cursor:pointer;">&times;</button>
                <h2 style="color:#1E3A8A; font-size:1.1em; font-weight:700; margin-bottom:18px;">' . __('Add New County', 'georgia-property-tax-calculator') . '</h2>';
                echo '<form method="post">';
                echo '<table class="form-table">';
                echo '<tr><th><label for="add_county_name">' . __('County Name', 'georgia-property-tax-calculator') . '</label></th><td><input type="text" name="add_county_name" id="add_county_name" value="" required /></td></tr>';
                echo '<tr><th><label for="add_millage_rate">' . __('Millage Rate', 'georgia-property-tax-calculator') . '</label></th><td><input type="number" step="0.01" name="add_millage_rate" id="add_millage_rate" value="" required /></td></tr>';
                echo '<tr><th><label for="add_homestead_exemption">' . __('Homestead Exemption', 'georgia-property-tax-calculator') . '</label></th><td><input type="number" step="0.01" name="add_homestead_exemption" id="add_homestead_exemption" value="" required /></td></tr>';
                echo '<tr><th><label for="add_senior_exemption">' . __('Senior Exemption', 'georgia-property-tax-calculator') . '</label></th><td><input type="number" step="0.01" name="add_senior_exemption" id="add_senior_exemption" value="" required /></td></tr>';
                echo '<tr><th><label for="add_disabled_exemption">' . __('Disabled Exemption', 'georgia-property-tax-calculator') . '</label></th><td><input type="number" step="0.01" name="add_disabled_exemption" id="add_disabled_exemption" value="" required /></td></tr>';
                echo '<tr><th><label for="add_assessment_ratio">' . __('Assessment Ratio', 'georgia-property-tax-calculator') . '</label></th><td><input type="number" step="0.01" name="add_assessment_ratio" id="add_assessment_ratio" value="0.40" required /></td></tr>';
                echo '</table>';
                echo '<p><input type="submit" name="gptc_add_county_submit" class="button button-primary" value="' . __('Add County', 'georgia-property-tax-calculator') . '" /></p>';
                echo '</form>';
            echo '</div>';
        echo '</div>';
        ?>
        <script>
        jQuery(document).ready(function($){
            // Add County Modal
            $('#gptc-add-county-btn').on('click', function(e){
                e.preventDefault();
                $('#gptc-add-county-modal').fadeIn(150);
            });
            // Edit County Modal
            $('.gptc-edit-county').on('click', function(e){
                e.preventDefault();
                $('#gptc_edit_county_name_modal').val($(this).data('county'));
                $('#gptc_edit_millage_rate_modal').val($(this).data('millage'));
                $('#gptc_edit_homestead_exemption_modal').val($(this).data('homestead'));
                $('#gptc_edit_senior_exemption_modal').val($(this).data('senior'));
                $('#gptc_edit_disabled_exemption_modal').val($(this).data('disabled'));
                $('#gptc_edit_assessment_ratio_modal').val($(this).data('assessment'));
                $('#gptc-edit-county-modal').fadeIn(150);
            });
            // Modal close
            $('.gptc-modal-close, .gptc-modal').on('click', function(e){
                if (e.target === this) {
                    $('.gptc-modal').fadeOut(150);
                }
            });
            $('.gptc-modal-card').on('click', function(e){
                e.stopPropagation();
            });
            // Delete
            $('.gptc-delete-county').on('click', function(e){
                e.preventDefault();
                if(confirm('<?php _e('Are you sure you want to delete this county? This action cannot be undone.', 'georgia-property-tax-calculator'); ?>')) {
                    var countyName = $(this).data('county');
                    var form = $('<form method="post" action=""><input type="hidden" name="gptc_delete_county" value="1" /><input type="hidden" name="delete_county_name" value="' + countyName + '" /></form>');
                    $('body').append(form);
                    form.submit();
                }
            });
        });
        </script>
        <?php
        echo '</div>';
    }

    /**
     * Admin notices
     */
    public function admin_notices()
    {
        $google_key = get_option('gptc_google_maps_api_key');
        $rentcast_key = get_option('gptc_rentcast_api_key');

        if (empty($google_key) || empty($rentcast_key)) {
            echo '<div class="notice notice-warning is-dismissible">';
            echo '<p>' . sprintf(
                __('Georgia Property Tax Calculator: Please <a href="%s">configure your API keys</a> to enable full functionality.', 'georgia-property-tax-calculator'),
                admin_url('admin.php?page=gptc-settings')
            ) . '</p>';
            echo '</div>';
        }
    }
}
