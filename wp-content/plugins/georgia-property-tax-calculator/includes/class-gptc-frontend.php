<?php
/**
 * Frontend functionality for Georgia Property Tax Calculator
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class GPTC_Frontend {
    
    public function __construct() {
        // No specific frontend hooks needed here
        // Form rendering is handled by the shortcode class
    }
    
    /**
     * Render the tax calculator form
     */
    public function render_form($atts = array()) {
        // Default attributes
        $atts = shortcode_atts(array(
            'title' => __('Estimate Your Property Tax Savings', 'georgia-property-tax-calculator'),
            'show_title' => true,
            'class' => 'gptc-form-container'
        ), $atts);

        // Dynamic property types and states from admin settings
        $property_types_json = get_option('gptc_property_types', json_encode(array('home' => __('Home', 'georgia-property-tax-calculator'), 'commercial' => __('Commercial', 'georgia-property-tax-calculator'))));
        $property_types = json_decode($property_types_json, true);
        if (!is_array($property_types)) {
            $property_types = array('home' => __('Home', 'georgia-property-tax-calculator'), 'commercial' => __('Commercial', 'georgia-property-tax-calculator'));
        }
        $states_json = get_option('gptc_states', json_encode(array('GA' => __('Georgia (GA)', 'georgia-property-tax-calculator'))));
        $states = json_decode($states_json, true);
        if (!is_array($states)) {
            $states = array('GA' => __('Georgia (GA)', 'georgia-property-tax-calculator'));
        }
        
        ob_start();
        ?>
        <div class="<?php echo esc_attr($atts['class']); ?>">
            <?php if ($atts['show_title']): ?>
                <div class="gptc-form-header">
                    <h3 class="gptc-form-title"><?php echo esc_html($atts['title']); ?></h3>
                </div>
            <?php endif; ?>
            
            <div class="gptc-form-wrapper">
                <div class="gptc-form-content">
                    <form id="gptc-calculator-form" class="gptc-form" method="post">
                        <?php wp_nonce_field('gptc_nonce', 'gptc_nonce'); ?>
                        
                        <div class="gptc-form-row">
                            <div class="gptc-form-group gptc-half">
                                <label for="gptc_name"><?php _e('Name', 'georgia-property-tax-calculator'); ?> <span class="required">*</span></label>
                                <input type="text" id="gptc_name" name="name" required class="gptc-input" placeholder="<?php _e('Enter your full name', 'georgia-property-tax-calculator'); ?>">
                            </div>
                            
                            <div class="gptc-form-group gptc-half">
                                <label for="gptc_email"><?php _e('Email', 'georgia-property-tax-calculator'); ?> <span class="required">*</span></label>
                                <input type="email" id="gptc_email" name="email" required class="gptc-input" placeholder="<?php _e('Enter your email address', 'georgia-property-tax-calculator'); ?>">
                            </div>
                        </div>
                        
                        <div class="gptc-form-group">
                            <label for="gptc_address"><?php _e('Property Address', 'georgia-property-tax-calculator'); ?> <span class="required">*</span></label>
                            <input type="text" id="gptc_address" name="address" required class="gptc-input" placeholder="<?php _e('Enter full property address', 'georgia-property-tax-calculator'); ?>">
                            <small class="gptc-help-text"><?php _e('Include street address, city, state, and ZIP code', 'georgia-property-tax-calculator'); ?></small>
                        </div>
                        
                        <div class="gptc-form-group">
                            <label for="gptc_market_value"><?php _e('Market Value / Last Tax Bill', 'georgia-property-tax-calculator'); ?></label>
                            <div class="gptc-input-wrapper">
                                <span class="gptc-input-prefix">$</span>
                                <input type="number" id="gptc_market_value" name="market_value" class="gptc-input gptc-input-with-prefix" placeholder="<?php _e('Enter property value', 'georgia-property-tax-calculator'); ?>" min="0" step="1000">
                            </div>
                            <small class="gptc-help-text"><?php _e('Leave blank to auto-fetch current market value', 'georgia-property-tax-calculator'); ?></small>
                        </div>
                        
                        <div class="gptc-form-row">
                            <div class="gptc-form-group gptc-half">
                                <label for="gptc_property_type"><?php _e('Property Type', 'georgia-property-tax-calculator'); ?></label>
                                <div class="gptc-button-group">
                                    <?php foreach ($property_types as $type_key => $type_label): ?>
                                        <input type="radio" id="gptc_type_<?php echo esc_attr($type_key); ?>" name="property_type" value="<?php echo esc_attr($type_key); ?>" <?php echo $type_key === 'home' ? 'checked' : ''; ?>>
                                        <label for="gptc_type_<?php echo esc_attr($type_key); ?>" class="gptc-button-option<?php echo $type_key === 'home' ? ' gptc-button-active' : ''; ?>"><?php echo esc_html($type_label); ?></label>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <div class="gptc-form-group gptc-half">
                                <label for="gptc_state"><?php _e('State', 'georgia-property-tax-calculator'); ?></label>
                                <select id="gptc_state" name="state" class="gptc-select">
                                    <?php foreach ($states as $state_key => $state_label): ?>
                                        <option value="<?php echo esc_attr($state_key); ?>" <?php echo $state_key === 'GA' ? 'selected' : ''; ?>><?php echo esc_html($state_label); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="gptc-savings-preview" id="gptc-savings-preview" style="display:none;">
                            <div class="gptc-savings-text">
                                <?php _e('You could save', 'georgia-property-tax-calculator'); ?>
                                <span class="gptc-savings-amount" id="gptc-savings-amount"></span>
                            </div>
                        </div>
                        
                        <div class="gptc-form-submit">
                            <button type="submit" class="gptc-submit-btn">
                                <span class="gptc-btn-text"><?php _e('Calculate', 'georgia-property-tax-calculator'); ?></span>
                                <span class="gptc-btn-loading" style="display: none;">
                                    <span class="gptc-spinner"></span>
                                    <?php _e('Calculating...', 'georgia-property-tax-calculator'); ?>
                                </span>
                            </button>
                        </div>
                        
                        <div id="gptc-result" class="gptc-result" style="display: none;"></div>
                        <div id="gptc-error" class="gptc-error" style="display: none;"></div>
                    </form>
                </div>
            </div>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            // Property type button toggle
            $('.gptc-button-option').on('click', function() {
                $('.gptc-button-option').removeClass('gptc-button-active');
                $(this).addClass('gptc-button-active');
            });
            
            // Form submission
            $('#gptc-calculator-form').on('submit', function(e) {
                e.preventDefault();
                
                var $form = $(this);
                var $submitBtn = $form.find('.gptc-submit-btn');
                var $btnText = $submitBtn.find('.gptc-btn-text');
                var $btnLoading = $submitBtn.find('.gptc-btn-loading');
                var $result = $('#gptc-result');
                var $error = $('#gptc-error');
                var $savingsPreview = $('#gptc-savings-preview');
                var $savingsAmount = $('#gptc-savings-amount');
                
                // Reset previous results
                $result.hide();
                $error.hide();
                $savingsPreview.hide();
                $savingsAmount.text(''); // Clear previous savings
                
                // Show loading state
                $btnText.hide();
                $btnLoading.show();
                $submitBtn.prop('disabled', true);
                
                // Collect form data
                var formData = {
                    action: 'gptc_calculate_tax',
                    nonce: $form.find('[name="gptc_nonce"]').val(),
                    name: $form.find('[name="name"]').val(),
                    email: $form.find('[name="email"]').val(),
                    address: $form.find('[name="address"]').val(),
                    market_value: $form.find('[name="market_value"]').val(),
                    property_type: $form.find('[name="property_type"]:checked').val(),
                    state: $form.find('[name="state"]').val()
                };
                
                // Submit via AJAX
                $.post(gptc_ajax.ajax_url, formData)
                    .done(function(response) {
                        if (response.success) {
                            $result.html(response.data.html).show();
                            // Scroll to result
                            $('html, body').animate({
                                scrollTop: $result.offset().top - 50
                            }, 500);

                            // Update savings preview if available
                            if (response.data.savings_amount) {
                                $savingsAmount.text(response.data.savings_amount);
                                $savingsPreview.show();
                            }
                        } else {
                            $error.html('<p>' + response.data + '</p>').show();
                        }
                    })
                    .fail(function() {
                        $error.html('<p>' + gptc_ajax.error_text + '</p>').show();
                    })
                    .always(function() {
                        // Reset button state
                        $btnText.show();
                        $btnLoading.hide();
                        $submitBtn.prop('disabled', false);
                    });
            });
        });
        </script>
        <?php
        return ob_get_clean();
    }
}
