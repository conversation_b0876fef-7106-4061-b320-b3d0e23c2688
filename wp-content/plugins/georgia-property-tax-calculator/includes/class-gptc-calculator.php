<?php

/**
 * Tax calculation engine for Georgia Property Tax Calculator
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class GPTC_Calculator
{

    private $api_handler;
    private $database;

    public function __construct()
    {
        $this->api_handler = new GPTC_API_Handler();
        $this->database = new GPTC_Database();
    }

    /**
     * Main tax calculation method
     */
    public function calculate_tax($form_data)
    {
        try {
            // Step 1: Get property data (address, county, market value)
            $property_data = $this->get_property_data($form_data);

            if (!$property_data['success']) {
                return array(
                    'success' => false,
                    'message' => implode('; ', $property_data['errors'])
                );
            }

            error_log(print_r($property_data, true));
            // Step 2: Get county tax data
            $county_name = $property_data['data']['county'];
            // Only support Georgia counties
            $georgia_counties = array(
                'Fulton', 'DeKalb', 'Gwinnett', 'Cobb', 'Clayton', 'Cherokee', '<PERSON>',
                '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', 'Paul<PERSON>', 'Richmond',
                '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>ibb', '<PERSON>', '<PERSON>ow', '<PERSON>', 'Cowe<PERSON>'
            );
            if (!in_array($county_name, $georgia_counties)) {
                return array(
                    'success' => false,
                    'message' => __('This calculator only supports Georgia counties. Your property is in: ', 'georgia-property-tax-calculator') . $county_name
                );
            }

            $county_data = $this->get_county_tax_data($county_name);

            // Check for DB error (table missing)
            global $wpdb;
            if ($wpdb->last_error && strpos($wpdb->last_error, 'gptc_counties') !== false) {
                return array(
                    'success' => false,
                    'message' => __('County tax data table is missing. Please contact the site administrator to reinstall or repair the plugin database.', 'georgia-property-tax-calculator')
                );
            }

            if (!$county_data) {
                return array(
                    'success' => false,
                    'message' => sprintf(__('No tax data available for %s County. Please contact support.', 'georgia-property-tax-calculator'), $county_name)
                );
            }

            // Step 3: Calculate taxes
            $calculation = $this->perform_tax_calculation(
                $property_data['data']['market_value'],
                $county_data,
                $form_data['property_type']
            );

            // Step 4: Calculate potential savings
            $savings = $this->calculate_potential_savings($calculation);

            // Step 5: Prepare result
            $result = array(
                'success' => true,
                'data' => array(
                    'address' => $property_data['data']['address'],
                    'county' => $property_data['data']['county'],
                    'market_value' => $property_data['data']['market_value'],
                    'assessed_value' => $calculation['assessed_value'],
                    'taxable_value' => $calculation['taxable_value'],
                    'annual_tax' => $calculation['annual_tax'],
                    'monthly_tax' => $calculation['monthly_tax'],
                    'potential_savings' => $savings['annual_savings'],
                    'monthly_savings' => $savings['monthly_savings'],
                    'exemptions_applied' => $calculation['exemptions_applied'],
                    'millage_rate' => $county_data['millage_rate'],
                    'assessment_ratio' => $county_data['assessment_ratio'],
                    'html' => $this->generate_result_html($calculation, $savings, $property_data['data'], $county_data)
                )
            );

            return $result;
        } catch (Exception $e) {
            error_log('GPTC Calculation Error: ' . $e->getMessage());
            return array(
                'success' => false,
                'message' => __('An error occurred during calculation. Please try again.', 'georgia-property-tax-calculator')
            );
        }
    }

    /**
     * Get property data using API handler
     */
    private function get_property_data($form_data)
    {
        $user_value = !empty($form_data['market_value']) ? floatval($form_data['market_value']) : null;

        $result = $this->api_handler->get_property_data($form_data['address'], $user_value);

        // If API fails, try to extract county from address and use fallback value
        if (!$result['success']) {
            $fallback_county = $this->extract_county_from_address($form_data['address']);

            if ($fallback_county) {
                $fallback_value = $user_value ?: $this->api_handler->get_fallback_property_value($fallback_county, $form_data['property_type']);

                $result = array(
                    'success' => true,
                    'data' => array(
                        'address' => $form_data['address'],
                        'county' => $fallback_county,
                        'market_value' => $fallback_value,
                        'value_source' => 'fallback'
                    ),
                    'errors' => $result['errors']
                );
            }
        }

        return $result;
    }

    /**
     * Extract county from address string (fallback method)
     */
    private function extract_county_from_address($address)
    {
        // Common Georgia counties
        $georgia_counties = array(
            'Fulton',
            'DeKalb',
            'Gwinnett',
            'Cobb',
            'Clayton',
            'Cherokee',
            'Henry',
            'Forsyth',
            'Douglas',
            'Fayette',
            'Hall',
            'Paulding',
            'Richmond',
            'Chatham',
            'Muscogee',
            'Bibb',
            'Houston',
            'Bartow',
            'Carroll',
            'Coweta'
        );

        $address_upper = strtoupper($address);

        foreach ($georgia_counties as $county) {
            if (strpos($address_upper, strtoupper($county)) !== false) {
                return $county;
            }
        }

        // Default to Fulton if no county found and address contains Atlanta or GA
        if (strpos($address_upper, 'ATLANTA') !== false || strpos($address_upper, 'GA') !== false) {
            return 'Fulton';
        }

        return null;
    }

    /**
     * Get county tax data from database
     */
    private function get_county_tax_data($county_name)
    {
        // Normalize: remove ' County' suffix if present
        $normalized_county = preg_replace('/\s+County$/i', '', trim($county_name));
        return $this->database->get_county_data($normalized_county, 'GA');
    }

    /**
     * Perform the actual tax calculation
     */
    private function perform_tax_calculation($market_value, $county_data, $property_type)
    {
        // Georgia uses 40% assessment ratio
        $assessment_ratio = floatval($county_data['assessment_ratio']);
        $assessed_value = $market_value * $assessment_ratio;

        // Apply exemptions
        $exemptions_applied = array();
        $total_exemptions = 0;

        // Homestead exemption (only for residential properties)
        if ($property_type === 'home' && $county_data['homestead_exemption'] > 0) {
            $homestead_exemption = floatval($county_data['homestead_exemption']);
            $exemptions_applied['homestead'] = $homestead_exemption;
            $total_exemptions += $homestead_exemption;
        }

        // Calculate taxable value
        $taxable_value = max(0, $assessed_value - $total_exemptions);

        // Calculate annual tax
        $millage_rate = floatval($county_data['millage_rate']);
        $annual_tax = ($taxable_value / 1000) * $millage_rate;

        // Calculate monthly tax
        $monthly_tax = $annual_tax / 12;

        return array(
            'market_value' => $market_value,
            'assessed_value' => $assessed_value,
            'total_exemptions' => $total_exemptions,
            'exemptions_applied' => $exemptions_applied,
            'taxable_value' => $taxable_value,
            'annual_tax' => $annual_tax,
            'monthly_tax' => $monthly_tax,
            'millage_rate' => $millage_rate,
            'assessment_ratio' => $assessment_ratio
        );
    }

    /**
     * Calculate potential savings
     */
    private function calculate_potential_savings($calculation)
    {
        // Estimate potential savings based on common tax reduction strategies
        // This is a simplified calculation - actual savings depend on many factors

        $current_annual_tax = $calculation['annual_tax'];

        // Potential savings scenarios:
        // 1. Additional exemptions (senior, disability, etc.) - 5-15%
        // 2. Assessment appeals - 10-25%
        // 3. Property tax consulting optimization - 15-30%

        // Conservative estimate: 15% average savings
        $savings_percentage = 0.15;

        // For higher value properties, potential savings might be higher
        if ($calculation['market_value'] > 500000) {
            $savings_percentage = 0.20;
        } elseif ($calculation['market_value'] > 300000) {
            $savings_percentage = 0.18;
        }

        $annual_savings = $current_annual_tax * $savings_percentage;
        $monthly_savings = $annual_savings / 12;

        return array(
            'annual_savings' => $annual_savings,
            'monthly_savings' => $monthly_savings,
            'savings_percentage' => $savings_percentage * 100
        );
    }

    /**
     * Generate HTML result for display
     */
    private function generate_result_html($calculation, $savings, $property_data, $county_data)
    {
        ob_start();
?>
        <div class="gptc-result-container">
            <div class="gptc-result-header">
                <h4><?php _e('Your Property Tax Estimate', 'georgia-property-tax-calculator'); ?></h4>
                <div class="gptc-result-address"><?php echo esc_html($property_data['address']); ?></div>
                <div class="gptc-result-county"><?php echo esc_html($property_data['county']); ?> <?php _e('County, Georgia', 'georgia-property-tax-calculator'); ?></div>
            </div>

            <div class="gptc-result-grid">
                <div class="gptc-result-item">
                    <div class="gptc-result-label"><?php _e('Market Value', 'georgia-property-tax-calculator'); ?></div>
                    <div class="gptc-result-value">$<?php echo number_format($calculation['market_value']); ?></div>
                </div>

                <div class="gptc-result-item">
                    <div class="gptc-result-label"><?php _e('Assessed Value', 'georgia-property-tax-calculator'); ?></div>
                    <div class="gptc-result-value">$<?php echo number_format($calculation['assessed_value']); ?></div>
                    <div class="gptc-result-note"><?php echo ($calculation['assessment_ratio'] * 100); ?>% <?php _e('of market value', 'georgia-property-tax-calculator'); ?></div>
                </div>

                <?php if (!empty($calculation['exemptions_applied'])): ?>
                    <div class="gptc-result-item">
                        <div class="gptc-result-label"><?php _e('Exemptions Applied', 'georgia-property-tax-calculator'); ?></div>
                        <div class="gptc-result-value">-$<?php echo number_format($calculation['total_exemptions']); ?></div>
                        <?php foreach ($calculation['exemptions_applied'] as $type => $amount): ?>
                            <div class="gptc-result-note"><?php echo ucfirst($type); ?>: $<?php echo number_format($amount); ?></div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>

                <div class="gptc-result-item">
                    <div class="gptc-result-label"><?php _e('Taxable Value', 'georgia-property-tax-calculator'); ?></div>
                    <div class="gptc-result-value">$<?php echo number_format($calculation['taxable_value']); ?></div>
                </div>

                <div class="gptc-result-item gptc-result-highlight">
                    <div class="gptc-result-label"><?php _e('Annual Property Tax', 'georgia-property-tax-calculator'); ?></div>
                    <div class="gptc-result-value">$<?php echo number_format($calculation['annual_tax']); ?></div>
                    <div class="gptc-result-note">$<?php echo number_format($calculation['monthly_tax']); ?> <?php _e('per month', 'georgia-property-tax-calculator'); ?></div>
                </div>
            </div>

            <div class="gptc-savings-section">
                <div class="gptc-savings-header">
                    <h5><?php _e('Potential Savings with Our Help', 'georgia-property-tax-calculator'); ?></h5>
                </div>

                <div class="gptc-savings-amount">
                    <div class="gptc-savings-annual">
                        <span class="gptc-savings-label"><?php _e('You could save up to', 'georgia-property-tax-calculator'); ?></span>
                        <span class="gptc-savings-value">$<?php echo number_format($savings['annual_savings']); ?>/year</span>
                    </div>
                    <div class="gptc-savings-monthly">
                        <span class="gptc-savings-value">$<?php echo number_format($savings['monthly_savings']); ?>/month</span>
                    </div>
                </div>

                <div class="gptc-cta-section">
                    <p><?php _e('Our property tax experts can help you reduce your tax burden through:', 'georgia-property-tax-calculator'); ?></p>
                    <ul>
                        <li><?php _e('Property assessment appeals', 'georgia-property-tax-calculator'); ?></li>
                        <li><?php _e('Additional exemption applications', 'georgia-property-tax-calculator'); ?></li>
                        <li><?php _e('Tax reduction strategies', 'georgia-property-tax-calculator'); ?></li>
                        <li><?php _e('Ongoing monitoring and optimization', 'georgia-property-tax-calculator'); ?></li>
                    </ul>

                    <div class="gptc-contact-info">
                        <div class="gptc-contact-item">
                            <strong><?php _e('Call us today:', 'georgia-property-tax-calculator'); ?></strong>
                            <a href="tel:************">************</a>
                        </div>
                        <div class="gptc-contact-item">
                            <strong><?php _e('Email:', 'georgia-property-tax-calculator'); ?></strong>
                            <a href="mailto:<EMAIL>"><EMAIL></a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="gptc-disclaimer">
                <p><small><?php _e('* This is an estimate based on current tax rates and standard exemptions. Actual taxes may vary. Potential savings are estimates based on typical results and are not guaranteed.', 'georgia-property-tax-calculator'); ?></small></p>
            </div>
        </div>
<?php
        return ob_get_clean();
    }
}
