<?php
/**
 * Database handler for Georgia Property Tax Calculator
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class GPTC_Database {
    
    private $table_submissions;
    private $table_counties;
    
    public function __construct() {
        global $wpdb;
        $this->table_submissions = $wpdb->prefix . 'gptc_submissions';
        $this->table_counties = $wpdb->prefix . 'gptc_counties';
    }
    
    /**
     * Create database tables
     */
    public function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Submissions table
        $sql_submissions = "CREATE TABLE {$this->table_submissions} (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            email varchar(255) NOT NULL,
            address text NOT NULL,
            market_value decimal(12,2) DEFAULT NULL,
            property_type varchar(50) NOT NULL,
            state varchar(10) NOT NULL,
            county varchar(255) DEFAULT NULL,
            assessed_value decimal(12,2) DEFAULT NULL,
            annual_tax decimal(12,2) DEFAULT NULL,
            potential_savings decimal(12,2) DEFAULT NULL,
            submission_date datetime DEFAULT CURRENT_TIMESTAMP,
            ip_address varchar(45) DEFAULT NULL,
            user_agent text DEFAULT NULL,
            PRIMARY KEY (id),
            KEY email (email),
            KEY submission_date (submission_date)
        ) $charset_collate;";
        
        // Counties table for Georgia tax data
        $sql_counties = "CREATE TABLE {$this->table_counties} (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            county_name varchar(255) NOT NULL,
            state varchar(10) NOT NULL,
            millage_rate decimal(6,3) NOT NULL,
            homestead_exemption decimal(10,2) DEFAULT 0,
            senior_exemption decimal(10,2) DEFAULT 0,
            disabled_exemption decimal(10,2) DEFAULT 0,
            assessment_ratio decimal(4,3) DEFAULT 0.40,
            last_updated datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY county_state (county_name, state)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql_submissions);
        dbDelta($sql_counties);
        
        // Insert default Georgia county data
        $this->insert_default_county_data();
    }
    
    /**
     * Insert default Georgia county tax data
     */
    private function insert_default_county_data() {
        global $wpdb;
        
        // Check if data already exists
        $count = $wpdb->get_var("SELECT COUNT(*) FROM {$this->table_counties} WHERE state = 'GA'");
        if ($count > 0) {
            return; // Data already exists
        }
        
        // Georgia county data with millage rates and exemptions
        $georgia_counties = array(
            array('Fulton', 'GA', 32.5, 2000, 4000, 10000),
            array('DeKalb', 'GA', 28.7, 2000, 4000, 10000),
            array('Gwinnett', 'GA', 25.8, 2000, 4000, 10000),
            array('Cobb', 'GA', 24.9, 2000, 4000, 10000),
            array('Clayton', 'GA', 35.2, 2000, 4000, 10000),
            array('Cherokee', 'GA', 22.1, 2000, 4000, 10000),
            array('Henry', 'GA', 26.3, 2000, 4000, 10000),
            array('Forsyth', 'GA', 19.8, 2000, 4000, 10000),
            array('Douglas', 'GA', 28.4, 2000, 4000, 10000),
            array('Fayette', 'GA', 21.7, 2000, 4000, 10000),
            array('Hall', 'GA', 24.6, 2000, 4000, 10000),
            array('Paulding', 'GA', 25.9, 2000, 4000, 10000),
            array('Richmond', 'GA', 31.8, 2000, 4000, 10000),
            array('Chatham', 'GA', 29.4, 2000, 4000, 10000),
            array('Muscogee', 'GA', 33.7, 2000, 4000, 10000),
            array('Bibb', 'GA', 36.2, 2000, 4000, 10000),
            array('Houston', 'GA', 27.1, 2000, 4000, 10000),
            array('Bartow', 'GA', 26.8, 2000, 4000, 10000),
            array('Carroll', 'GA', 28.9, 2000, 4000, 10000),
            array('Coweta', 'GA', 23.4, 2000, 4000, 10000)
        );
        
        foreach ($georgia_counties as $county) {
            $wpdb->insert(
                $this->table_counties,
                array(
                    'county_name' => $county[0],
                    'state' => $county[1],
                    'millage_rate' => $county[2],
                    'homestead_exemption' => $county[3],
                    'senior_exemption' => $county[4],
                    'disabled_exemption' => $county[5],
                    'assessment_ratio' => 0.40
                ),
                array('%s', '%s', '%f', '%f', '%f', '%f', '%f')
            );
        }
    }
    
    /**
     * Store form submission
     */
    public function store_submission($form_data, $calculation_data) {
        global $wpdb;
        
        $data = array(
            'name' => $form_data['name'],
            'email' => $form_data['email'],
            'address' => $form_data['address'],
            'market_value' => $form_data['market_value'],
            'property_type' => $form_data['property_type'],
            'state' => $form_data['state'],
            'county' => isset($calculation_data['county']) ? $calculation_data['county'] : null,
            'assessed_value' => isset($calculation_data['assessed_value']) ? $calculation_data['assessed_value'] : null,
            'annual_tax' => isset($calculation_data['annual_tax']) ? $calculation_data['annual_tax'] : null,
            'potential_savings' => isset($calculation_data['potential_savings']) ? $calculation_data['potential_savings'] : null,
            'ip_address' => $this->get_client_ip(),
            'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : ''
        );
        
        $result = $wpdb->insert($this->table_submissions, $data);
        
        if ($result === false) {
            error_log('GPTC Database Error: ' . $wpdb->last_error);
            return false;
        }
        
        return $wpdb->insert_id;
    }
    
    /**
     * Get county data by name
     */
    public function get_county_data($county_name, $state = 'GA') {
        global $wpdb;
        
        $result = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM {$this->table_counties} WHERE county_name = %s AND state = %s",
                $county_name,
                $state
            ),
            ARRAY_A
        );
        
        return $result;
    }
    
    /**
     * Get all submissions (for admin)
     */
    public function get_submissions($limit = 50, $offset = 0) {
        global $wpdb;
        
        $results = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT * FROM {$this->table_submissions} ORDER BY submission_date DESC LIMIT %d OFFSET %d",
                $limit,
                $offset
            ),
            ARRAY_A
        );
        
        return $results;
    }
    
    /**
     * Get submission count
     */
    public function get_submission_count() {
        global $wpdb;
        
        return $wpdb->get_var("SELECT COUNT(*) FROM {$this->table_submissions}");
    }
    
    /**
     * Get client IP address
     */
    private function get_client_ip() {
        $ip_keys = array('HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR');
        
        foreach ($ip_keys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                foreach (explode(',', $_SERVER[$key]) as $ip) {
                    $ip = trim($ip);
                    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                        return $ip;
                    }
                }
            }
        }
        
        return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '0.0.0.0';
    }
    
    /**
     * Update county data
     */
    public function update_county_data($county_name, $state, $data) {
        global $wpdb;
        
        return $wpdb->update(
            $this->table_counties,
            $data,
            array('county_name' => $county_name, 'state' => $state),
            array('%f', '%f', '%f', '%f', '%f'),
            array('%s', '%s')
        );
    }

    /**
     * Add a new county
     */
    public function add_county_data($data) {
        global $wpdb;
        $wpdb->insert($this->table_counties, $data);
    }

    /**
     * Delete a county
     */
    public function delete_county_data($county_name, $state = 'GA') {
        global $wpdb;
        $wpdb->delete($this->table_counties, array('county_name' => $county_name, 'state' => $state));
    }

    /**
     * Get all counties for a given state
     */
    public function get_all_counties($state = 'GA') {
        global $wpdb;
        return $wpdb->get_results(
            $wpdb->prepare("SELECT * FROM {$this->table_counties} WHERE state = %s ORDER BY county_name ASC", $state),
            ARRAY_A
        );
    }
}
